@startuml Student_Online_Exam
!theme plain
title Student - Thi trực tuyến với Tr<PERSON>i nghiệm Hiện đại

actor Student
participant "StudentController" as SC
participant "ApplicationDbContext" as DB
participant "UserManager" as UM
participant "JavaScript Timer" as JS
participant "AutoSave Service" as AS

== Xem Bài thi Có sẵn ==
Student -> SC: GET /Student/AvailableExams
activate SC

SC -> UM: GetUserAsync(User)
activate UM
UM --> SC: Current Student
deactivate UM

SC -> DB: ExamSchedules.Where(now >= StartTime && now <= EndTime && IsActive)
activate DB
DB --> SC: Available Exams
deactivate DB

SC -> DB: Submissions.Where(StudentUserId == currentUser.Id)
activate DB
DB --> SC: Student's Submissions
deactivate DB

SC --> Student: AvailableExamsViewModel with Status
deactivate SC

== Bắt đầu Làm bài ==
Student -> SC: GET /Student/TakeExam/{id}
activate SC

SC -> DB: ExamSchedules.Include(Exam, Submissions).FirstOrDefaultAsync(id)
activate DB
DB --> SC: Exam Details
deactivate DB

alt Already Submitted
    SC --> Student: Warning Message + Redirect
else Can Take Exam
    SC --> Student: TakeExamViewModel with Questions
    activate JS
    JS -> JS: StartCountdownTimer()
    JS -> AS: StartAutoSave() every 30 seconds
    activate AS
end
deactivate SC

== Auto-Save trong quá trình làm bài ==
loop Every 30 seconds
    AS -> SC: POST /Student/AutoSaveAnswers
    activate SC
    SC -> DB: Update or Insert temporary answers
    activate DB
    DB --> SC: Saved
    deactivate DB
    SC --> AS: Save Confirmed
    deactivate SC
end

== Nộp bài (Manual hoặc Auto) ==
alt Manual Submit
    Student -> SC: POST /Student/SubmitExam
else Auto Submit (Time Up)
    JS -> SC: POST /Student/SubmitExam (Auto)
end

activate SC

SC -> DB: Submissions.Add(newSubmission)
activate DB

loop for each answer
    SC -> DB: SubmissionAnswers.Add(answer)
end

SC -> DB: SaveChangesAsync()
DB --> SC: Submission Saved
deactivate DB

deactivate AS
deactivate JS

SC --> Student: Submission Successful + Confirmation
deactivate SC

== Xem Kết quả ==
Student -> SC: GET /Student/MySubmissions
activate SC

SC -> DB: Submissions.Include(Grade, ExamSchedule.Exam).Where(StudentUserId)
activate DB
DB --> SC: Student's Submissions with Grades
deactivate DB

SC --> Student: MySubmissionsViewModel with Results
deactivate SC

@enduml