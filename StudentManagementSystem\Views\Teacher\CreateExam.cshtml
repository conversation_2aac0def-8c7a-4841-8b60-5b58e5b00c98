@model StudentManagementSystem.ViewModels.CreateExamViewModel
@{
    ViewData["Title"] = "Tạo bài kiểm tra mới";
}

@section Styles {
    <link href="~/css/teacher-ui.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet" />
}

<div class="teacher-page-container">
    <!-- Welcome Header -->
    <div class="welcome-header mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div class="position-relative z-index-2">
                <h2 class="mb-2 fw-bold">
                    <i class="fas fa-file-plus me-2"></i>
                    Tạo bài kiểm tra mới 📝
                </h2>
                <p class="mb-0 opacity-75">Tạo bài kiểm tra mới cho lớp học của bạn</p>
            </div>
            <div class="text-end position-relative z-index-2">
                <a href="@Url.Action("Exams")" class="btn btn-light btn-lg px-4 py-2 rounded-pill shadow-sm">
                    <i class="fas fa-arrow-left me-2"></i> Quay lại danh sách
                </a>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="mb-0 text-success fw-bold">
                        <i class="fas fa-file-plus me-2"></i> Thông tin bài kiểm tra
                    </h5>
                </div>
            <div class="card-body">
                <form asp-action="CreateExam" method="post" enctype="multipart/form-data" class="form-modern">
                    <div asp-validation-summary="All" class="alert alert-danger" role="alert"></div>

                    <div class="form-group-modern">
                        <label asp-for="SubjectId" class="form-label-modern"></label>
                        <select asp-for="SubjectId" class="form-control-modern">
                            <option value="">-- Chọn môn học --</option>
                            @foreach (var subject in ViewBag.Subjects as IEnumerable<StudentManagementSystem.Models.Subject>)
                            {
                                <option value="@subject.Id">@subject.SubjectName (@subject.SubjectCode)</option>
                            }
                        </select>
                        <span asp-validation-for="SubjectId" class="form-error"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Title" class="form-label"></label>
                        <input asp-for="Title" class="form-control" placeholder="VD: Kiểm tra giữa kỳ môn Toán" />
                        <span asp-validation-for="Title" class="text-danger"></span>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="Description" class="form-label"></label>
                        <textarea asp-for="Description" class="form-control" rows="4" placeholder="Mô tả nội dung, yêu cầu, hình thức kiểm tra..."></textarea>
                        <span asp-validation-for="Description" class="text-danger"></span>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="ExamFile" class="form-label"></label>
                        <input asp-for="ExamFile" class="form-control" type="file" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" />
                        <span asp-validation-for="ExamFile" class="text-danger"></span>
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            Hỗ trợ file: PDF, Word (.doc, .docx), Hình ảnh (.jpg, .jpeg, .png). Tối đa 10MB.
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb"></i> Hướng dẫn:</h6>
                        <ul class="mb-0">
                            <li><strong>Tiêu đề:</strong> Đặt tên rõ ràng, dễ hiểu cho bài kiểm tra</li>
                            <li><strong>Mô tả:</strong> Ghi rõ nội dung, hình thức, thời gian làm bài</li>
                            <li><strong>File đề thi:</strong> Upload file đề thi (không bắt buộc, có thể thêm sau)</li>
                            <li><strong>Sau khi tạo:</strong> Bạn cần tạo lịch thi để học sinh có thể làm bài</li>
                        </ul>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="@Url.Action("Exams")" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Tạo bài kiểm tra
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Preview Section -->
<div class="row justify-content-center mt-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-eye"></i> Xem trước</h5>
            </div>
            <div class="card-body">
                <div id="preview-content">
                    <h6 class="text-muted">Tiêu đề bài kiểm tra sẽ hiển thị ở đây...</h6>
                    <p class="text-muted">Mô tả bài kiểm tra sẽ hiển thị ở đây...</p>
                    <div class="text-muted">
                        <i class="fas fa-file"></i> File đề thi: <span id="file-name">Chưa chọn file</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const titleInput = document.querySelector('input[name="Title"]');
            const descriptionInput = document.querySelector('textarea[name="Description"]');
            const fileInput = document.querySelector('input[name="ExamFile"]');
            
            const previewTitle = document.querySelector('#preview-content h6');
            const previewDescription = document.querySelector('#preview-content p');
            const previewFileName = document.querySelector('#file-name');
            
            // Preview title
            titleInput.addEventListener('input', function() {
                previewTitle.textContent = this.value || 'Tiêu đề bài kiểm tra sẽ hiển thị ở đây...';
                previewTitle.className = this.value ? 'text-dark' : 'text-muted';
            });
            
            // Preview description
            descriptionInput.addEventListener('input', function() {
                previewDescription.textContent = this.value || 'Mô tả bài kiểm tra sẽ hiển thị ở đây...';
                previewDescription.className = this.value ? 'text-dark' : 'text-muted';
            });
            
            // Preview file
            fileInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    previewFileName.textContent = this.files[0].name;
                    previewFileName.className = 'text-success';
                } else {
                    previewFileName.textContent = 'Chưa chọn file';
                    previewFileName.className = 'text-muted';
                }
            });
        });
    </script>
}
</div> <!-- End teacher-page-container -->
