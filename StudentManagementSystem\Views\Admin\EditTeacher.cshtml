@model StudentManagementSystem.ViewModels.EditUserViewModel
@{
    ViewData["Title"] = "Sửa thông tin giảng viên";
}

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h4><i class="fas fa-edit"></i> Sửa thông tin giảng viên</h4>
            </div>
            <div class="card-body">
                <form asp-action="EditTeacher" method="post">
                    <input asp-for="Id" type="hidden" />
                    <div asp-validation-summary="All" class="text-danger mb-3"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="FirstName" class="form-label"></label>
                                <input asp-for="FirstName" class="form-control" placeholder="Nhập tên" />
                                <span asp-validation-for="FirstName" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="LastName" class="form-label"></label>
                                <input asp-for="LastName" class="form-control" placeholder="Nhập họ" />
                                <span asp-validation-for="LastName" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Email" class="form-label"></label>
                                <input asp-for="Email" class="form-control" type="email" placeholder="<EMAIL>" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="EmployeeId" class="form-label"></label>
                                <input asp-for="EmployeeId" class="form-control" placeholder="VD: GV001" />
                                <span asp-validation-for="EmployeeId" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="DateOfBirth" class="form-label"></label>
                                <input asp-for="DateOfBirth" class="form-control" type="date" />
                                <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Salary" class="form-label"></label>
                                <input asp-for="Salary" class="form-control" type="number" min="0" step="100000" placeholder="VD: 15000000" />
                                <span asp-validation-for="Salary" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">Mật khẩu mới (để trống nếu không đổi)</label>
                        <input name="newPassword" class="form-control" type="password" placeholder="Nhập mật khẩu mới" />
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="Address" class="form-label"></label>
                        <textarea asp-for="Address" class="form-control" rows="3" placeholder="Nhập địa chỉ (tùy chọn)"></textarea>
                        <span asp-validation-for="Address" class="text-danger"></span>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="@Url.Action("Teachers")" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Cập nhật
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
