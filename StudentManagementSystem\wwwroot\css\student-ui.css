/* Student UI Enhancements */

/* Welcome Header Animation */
.welcome-header {
    background: linear-gradient(135deg, #a8e6cf 0%, #7fcdcd 50%, #81c784 100%);
    border-radius: 20px;
    padding: 25px;
    color: #2e7d32;
    margin-bottom: 30px;
    box-shadow: 0 15px 35px rgba(129, 199, 132, 0.3);
    position: relative;
    overflow: hidden;
}

.welcome-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }

    50% {
        transform: translateY(-10px) rotate(5deg);
    }
}

.welcome-header h2 {
    font-weight: 600;
    margin-bottom: 5px;
}

/* Quick Actions */
.quick-actions {
    margin-bottom: 30px;
}

.quick-action-btn {
    padding: 15px 10px;
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 2px solid;
    background: white;
    height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.quick-action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.quick-action-btn:hover::before {
    left: 100%;
}

/* Enhanced Statistics Cards */
.stat-card {
    transition: all 0.3s ease;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-sm {
    height: 4px;
    border-radius: 2px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 2px;
}

/* Card Hover Effects */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Button Enhancements */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
    border: none;
}

.btn-info {
    background: linear-gradient(45deg, #17a2b8, #117a8b);
    border: none;
}

.btn-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
    border: none;
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1055;
}

.toast {
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.toast-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.toast-error {
    background: linear-gradient(45deg, #dc3545, #c82333);
    color: white;
}

.toast-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
    color: white;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .welcome-header {
        padding: 15px;
        margin-bottom: 20px;
    }

    .welcome-header h2 {
        font-size: 1.5rem;
    }

    .quick-action-btn {
        height: 70px;
        padding: 10px 5px;
    }

    .quick-action-btn i {
        font-size: 1.2rem;
    }

    .stat-card {
        margin-bottom: 15px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .stat-card {
        background-color: #2d3748;
        color: white;
    }

    .quick-action-btn {
        background-color: #2d3748;
        color: white;
        border-color: #4a5568;
    }
}

/* Pulse Animation for Important Elements */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus States for Accessibility */
.btn:focus,
.quick-action-btn:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Badge Enhancements */
.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
    border-radius: 6px;
}

/* Table Enhancements */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    border-bottom: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

/* Form Enhancements */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Alert Enhancements */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Skeleton Loading */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }

    100% {
        background-position: -200% 0;
    }
}

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    border: none;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transition: all 0.3s ease;
    z-index: 1000;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
    color: white;
}

/* FAB Menu */
.fab-menu {
    position: fixed;
    bottom: 90px;
    right: 20px;
    z-index: 999;
}

.fab-item {
    display: block;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    text-decoration: none;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    animation: fabSlideIn 0.3s ease;
}

.fab-item:hover {
    transform: scale(1.1);
    color: white;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.fab-item:nth-child(1) {
    animation-delay: 0.1s;
}

.fab-item:nth-child(2) {
    animation-delay: 0.2s;
}

.fab-item:nth-child(3) {
    animation-delay: 0.3s;
}

@keyframes fabSlideIn {
    from {
        opacity: 0;
        transform: translateX(50px) scale(0.5);
    }

    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

/* Notification Dot */
.notification-dot {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 12px;
    height: 12px;
    background: #dc3545;
    border-radius: 50%;
    border: 2px solid white;
}

/* Success Checkmark Animation */
.checkmark {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    display: block;
    stroke-width: 2;
    stroke: #28a745;
    stroke-miterlimit: 10;
    box-shadow: inset 0px 0px 0px #28a745;
    animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
}

.checkmark__circle {
    stroke-dasharray: 166;
    stroke-dashoffset: 166;
    stroke-width: 2;
    stroke-miterlimit: 10;
    stroke: #28a745;
    fill: none;
    animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}

.checkmark__check {
    transform-origin: 50% 50%;
    stroke-dasharray: 48;
    stroke-dashoffset: 48;
    animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
}

@keyframes stroke {
    100% {
        stroke-dashoffset: 0;
    }
}

@keyframes scale {

    0%,
    100% {
        transform: none;
    }

    50% {
        transform: scale3d(1.1, 1.1, 1);
    }
}

@keyframes fill {
    100% {
        box-shadow: inset 0px 0px 0px 30px #28a745;
    }
}

/* Timeline Styles */
.timeline {
    position: relative;
    padding-left: 35px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 18px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(to bottom, #81c784, #a8e6cf, #e8f5e8);
    border-radius: 2px;
}

.timeline-item {
    position: relative;
    margin-bottom: 25px;
    padding-bottom: 15px;
}

.timeline-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
}

.timeline-item:last-child::after {
    content: '';
    position: absolute;
    left: -25px;
    bottom: -10px;
    width: 8px;
    height: 8px;
    background: #e8f5e8;
    border-radius: 50%;
    border: 2px solid #81c784;
}

.timeline-marker {
    position: absolute;
    left: -26px;
    top: 8px;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
    z-index: 2;
}

.timeline-content {
    background: linear-gradient(135deg, #f8fffe 0%, #f0f9f0 100%);
    padding: 18px;
    border-radius: 12px;
    border-left: 4px solid #81c784;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    position: relative;
}

.timeline-content:hover {
    background: linear-gradient(135deg, #f0f9f0 0%, #e8f5e8 100%);
    transform: translateX(8px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
}

.timeline-content h6 {
    color: #2e7d32;
    font-weight: 600;
    margin-bottom: 8px;
}

.timeline-content::before {
    content: '';
    position: absolute;
    top: 15px;
    left: -8px;
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid #f8fffe;
    transition: border-right-color 0.3s ease;
}

.timeline-content:hover::before {
    border-right-color: #f0f9f0;
}

/* Deadline Items */
.deadline-item {
    border-radius: 12px;
    background: linear-gradient(135deg, #f8fffe 0%, #f0f9f0 100%);
    transition: all 0.3s ease;
    border: 1px solid #e8f5e8;
}

.deadline-item:hover {
    background: linear-gradient(135deg, #f0f9f0 0%, #e8f5e8 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(129, 199, 132, 0.2);
}

/* Z-index utilities */
.z-index-2 {
    z-index: 2;
}

/* Enhanced card heights */
.h-100 {
    height: 100% !important;
}

/* Gradient progress bars */
.progress-bar.bg-gradient {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
    background-size: 1rem 1rem;
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% {
        background-position: 1rem 0;
    }

    100% {
        background-position: 0 0;
    }
}

/* Simple Statistics Cards */
.stat-card-simple {
    background: #ffffff;
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    height: 140px;
    display: flex;
    flex-direction: column;
}

.stat-card-simple:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

/* Clickable card styles */
a .stat-card-simple {
    cursor: pointer;
    transition: all 0.3s ease;
}

a .stat-card-simple:hover {
    transform: translateY(-6px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

a .stat-card-simple:active {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

/* Preserve text colors in clickable cards */
a .stat-card-simple .stat-number,
a .stat-card-simple .stat-label,
a .stat-card-simple .stat-icon {
    color: inherit;
}

a:hover .stat-card-simple .stat-number,
a:hover .stat-card-simple .stat-label,
a:hover .stat-card-simple .stat-icon {
    color: inherit;
}

.stat-card-header {
    height: 4px;
    width: 100%;
}

.stat-card-simple .stat-card-body {
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    flex: 1;
}

.stat-card-simple .stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin-bottom: 12px;
    color: #6c757d;
}

.stat-card-simple .stat-number {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 8px;
    color: #2c3e50;
}

.stat-card-simple .stat-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #6c757d;
    letter-spacing: 0.5px;
    margin-bottom: 12px;
    line-height: 1.2;
}

.stat-card-simple .stat-action {
    margin-top: auto;
}

.stat-card-simple .stat-link {
    color: #6c757d;
    text-decoration: none;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 6px;
    background: rgba(108, 117, 125, 0.05);
}

.stat-card-simple .stat-link:hover {
    color: #495057;
    background: rgba(108, 117, 125, 0.1);
    transform: translateX(2px);
}

.stat-card-simple .stat-warning {
    font-size: 0.7rem;
    color: #dc3545;
    font-weight: 600;
    margin-top: 8px;
    padding: 4px 8px;
    background: rgba(220, 53, 69, 0.1);
    border-radius: 6px;
    display: inline-block;
}

/* Color variants for simple cards */
.stat-card-danger .stat-card-header {
    background: #dc3545 !important;
}

.stat-card-danger .stat-icon {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.stat-card-primary .stat-card-header {
    background: #007bff !important;
}

.stat-card-primary .stat-icon {
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

.stat-card-success .stat-card-header {
    background: #28a745 !important;
}

.stat-card-success .stat-icon {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.stat-card-warning .stat-card-header {
    background: #ffc107 !important;
}

.stat-card-warning .stat-icon {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.stat-card-info .stat-card-header {
    background: #17a2b8 !important;
}

.stat-card-info .stat-icon {
    background: rgba(23, 162, 184, 0.1);
    color: #17a2b8;
}

/* Modern Statistics Cards (Keep for backward compatibility) */
.stat-card-modern {
    background: linear-gradient(135deg, #ffffff 0%, #f8fffe 100%);
    border: none;
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(129, 199, 132, 0.15);
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
    min-height: 180px;
    height: 180px;
    display: flex;
    flex-direction: column;
}

.stat-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #81c784, #a8e6cf, #7fcdcd);
    border-radius: 20px 20px 0 0;
}

.stat-card-modern:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 40px rgba(129, 199, 132, 0.25);
}

.stat-card-modern:hover::before {
    height: 6px;
    background: linear-gradient(90deg, #66bb6a, #81c784, #4db6ac);
}

.stat-card-body {
    padding: 24px 20px;
    display: flex;
    flex-direction: column;
    height: 100%;
    flex: 1;
    position: relative;
    justify-content: space-between;
}

.stat-icon-wrapper {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 2;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stat-card-modern:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.stat-content {
    flex: 1;
    padding-right: 70px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    min-height: 100px;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 8px;
    background: linear-gradient(135deg, #2e7d32, #388e3c, #4caf50);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: numberPulse 2s ease-in-out infinite;
    min-height: 2.75rem;
    display: flex;
    align-items: center;
}

@keyframes numberPulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

.stat-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #546e7a;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 12px;
    min-height: 2.5rem;
    display: flex;
    align-items: center;
    line-height: 1.3;
}

.stat-action {
    margin-top: auto;
}

.stat-link {
    color: #81c784;
    text-decoration: none;
    font-size: 0.8rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.stat-link:hover {
    color: #66bb6a;
    transform: translateX(3px);
}

.stat-link i {
    transition: transform 0.3s ease;
}

.stat-link:hover i {
    transform: translateX(3px);
}

.stat-warning {
    font-size: 0.75rem;
    color: #d32f2f;
    font-weight: 600;
    margin-top: 8px;
    padding: 4px 8px;
    background: rgba(244, 67, 54, 0.1);
    border-radius: 8px;
    display: inline-block;
}

.stat-progress {
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
}

.progress-ring {
    width: 40px;
    height: 40px;
    position: relative;
}

.progress-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: conic-gradient(#81c784 0deg, #e8f5e8 0deg);
    position: relative;
    transition: all 0.6s ease;
}

.progress-circle::before {
    content: '';
    position: absolute;
    top: 4px;
    left: 4px;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: white;
}

.progress-circle-animated {
    animation: progressSpin 3s ease-in-out infinite;
}

@keyframes progressSpin {
    0% {
        transform: rotate(0deg);
    }

    50% {
        transform: rotate(180deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Color Variants for Statistics Cards */
.stat-card-danger {
    background: linear-gradient(135deg, #fff5f5 0%, #ffebee 100%);
}

.stat-card-danger::before {
    background: linear-gradient(90deg, #f44336, #e57373, #ef5350);
}

.stat-card-danger .stat-number {
    background: linear-gradient(135deg, #c62828, #d32f2f, #f44336);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-card-primary {
    background: linear-gradient(135deg, #f3f7ff 0%, #e8f2ff 100%);
}

.stat-card-primary::before {
    background: linear-gradient(90deg, #2196f3, #42a5f5, #64b5f6);
}

.stat-card-primary .stat-number {
    background: linear-gradient(135deg, #1565c0, #1976d2, #2196f3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-card-success {
    background: linear-gradient(135deg, #f1f8e9 0%, #e8f5e8 100%);
}

.stat-card-success::before {
    background: linear-gradient(90deg, #4caf50, #66bb6a, #81c784);
}

.stat-card-success .stat-number {
    background: linear-gradient(135deg, #2e7d32, #388e3c, #4caf50);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-card-warning {
    background: linear-gradient(135deg, #fffbf0 0%, #fff8e1 100%);
}

.stat-card-warning::before {
    background: linear-gradient(90deg, #ff9800, #ffb74d, #ffcc02);
}

.stat-card-warning .stat-number {
    background: linear-gradient(135deg, #ef6c00, #f57c00, #ff9800);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-card-info {
    background: linear-gradient(135deg, #f0f8ff 0%, #e1f5fe 100%);
}

.stat-card-info::before {
    background: linear-gradient(90deg, #00bcd4, #26c6da, #4dd0e1);
}

.stat-card-info .stat-number {
    background: linear-gradient(135deg, #00838f, #0097a7, #00bcd4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Bootstrap subtle background utilities */
.bg-danger-subtle {
    background-color: rgba(244, 67, 54, 0.1) !important;
}

.bg-primary-subtle {
    background-color: rgba(33, 150, 243, 0.1) !important;
}

.bg-success-subtle {
    background-color: rgba(76, 175, 80, 0.1) !important;
}

.bg-warning-subtle {
    background-color: rgba(255, 152, 0, 0.1) !important;
}

.bg-info-subtle {
    background-color: rgba(0, 188, 212, 0.1) !important;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .stat-card-simple {
        height: 130px;
    }

    .stat-card-simple .stat-number {
        font-size: 1.75rem;
    }

    .stat-card-simple .stat-icon {
        width: 36px;
        height: 36px;
        font-size: 16px;
    }
}

@media (max-width: 768px) {
    .stat-card-simple {
        height: 120px;
    }

    .stat-card-simple .stat-card-body {
        padding: 16px;
    }

    .stat-card-simple .stat-number {
        font-size: 1.5rem;
    }

    .stat-card-simple .stat-label {
        font-size: 0.7rem;
    }

    .stat-card-simple .stat-icon {
        width: 32px;
        height: 32px;
        font-size: 14px;
        margin-bottom: 8px;
    }

    .stat-card-simple .stat-link {
        font-size: 0.7rem;
    }

    /* Modern cards responsive (keep for compatibility) */
    .stat-card-modern {
        min-height: 160px;
        height: 160px;
    }

    .stat-card-body {
        padding: 20px 16px;
    }

    .stat-number {
        font-size: 2rem;
        min-height: 2.2rem;
    }

    .stat-content {
        padding-right: 60px;
        min-height: 90px;
    }

    .stat-label {
        min-height: 2.2rem;
        font-size: 0.8rem;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .stat-progress {
        width: 32px;
        height: 32px;
    }

    .progress-circle {
        width: 32px;
        height: 32px;
    }

    .progress-circle::before {
        top: 3px;
        left: 3px;
        width: 26px;
        height: 26px;
    }
}

@media (max-width: 576px) {
    .stat-card-simple {
        height: 110px;
    }

    .stat-card-simple .stat-number {
        font-size: 1.25rem;
    }

    .stat-card-simple .stat-label {
        font-size: 0.65rem;
    }

    /* Modern cards responsive (keep for compatibility) */
    .stat-card-modern {
        min-height: 140px;
        height: 140px;
    }

    .stat-number {
        font-size: 1.75rem;
        min-height: 2rem;
    }

    .stat-label {
        font-size: 0.75rem;
        min-height: 2rem;
    }

    .stat-content {
        min-height: 80px;
    }
}

/* Force equal heights utility */
.equal-height-cards {
    display: flex;
    flex-wrap: wrap;
}

.equal-height-cards>[class*="col-"] {
    display: flex;
}

.equal-height-cards .stat-card-modern {
    width: 100%;
}

/* Additional consistency fixes */
.stat-warning {
    position: absolute;
    bottom: 45px;
    left: 20px;
    right: 70px;
}

.stat-action {
    margin-top: auto;
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 70px;
}

/* Enhanced Badge Animations */
.badge {
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Gradient Text */
.gradient-text {
    background: linear-gradient(45deg, #007bff, #28a745);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

/* Card Glow Effect */
.card-glow {
    box-shadow: 0 0 20px rgba(0, 123, 255, 0.3);
    transition: box-shadow 0.3s ease;
}

.card-glow:hover {
    box-shadow: 0 0 30px rgba(0, 123, 255, 0.5);
}

/* Floating Labels */
.floating-label {
    position: relative;
}

.floating-label input,
.floating-label textarea {
    padding-top: 20px;
}

.floating-label label {
    position: absolute;
    top: 0;
    left: 12px;
    font-size: 12px;
    color: #6c757d;
    transition: all 0.3s ease;
}

.floating-label input:focus+label,
.floating-label textarea:focus+label {
    color: #007bff;
    transform: translateY(-2px);
}

/* Ripple Effect */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
    width: 300px;
    height: 300px;
}

/* Smooth Transitions for All Interactive Elements */
* {
    transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
}

/* Print Styles */
@media print {

    .quick-actions,
    .fab,
    .toast-container,
    .btn {
        display: none !important;
    }

    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}