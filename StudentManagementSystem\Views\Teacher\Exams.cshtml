@model IEnumerable<StudentManagementSystem.Models.Exam>
@{
    ViewData["Title"] = "Quản lý bài kiểm tra";
}

@section Styles {
    <link href="~/css/admin-ui.css" rel="stylesheet" />
    <link href="~/css/teacher-ui.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet" />
}

<div class="teacher-page-container">
<!-- Welcome Header with Animation -->
<div class="welcome-header mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <div class="position-relative z-index-2">
            <h2 class="mb-2 animate__animated animate__fadeInLeft fw-bold">
                <i class="fas fa-file-alt me-2"></i>
                Quản lý bài kiểm tra 📝
            </h2>
            <p class="mb-0 opacity-75">Tạo và quản lý các bài kiểm tra cho lớp học của bạn</p>
        </div>
        <div class="text-end position-relative z-index-2">
            <div class="d-flex gap-2">
                <button class="btn btn-outline-light rounded-pill px-3" id="filterBtn">
                    <i class="fas fa-filter me-2"></i>Lọc
                </button>
                <a href="@Url.Action("CreateExam")" class="btn btn-light btn-lg px-4 py-2 rounded-pill shadow-sm">
                    <i class="fas fa-plus me-2"></i>Tạo bài kiểm tra mới
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Filter Panel -->
<div class="admin-action-card mb-4" id="filterPanel" style="display: none;">
    <div class="card-header">
        <h6 class="mb-0 fw-bold">
            <i class="fas fa-filter me-2"></i>Bộ lọc bài kiểm tra
        </h6>
    </div>
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Trạng thái</label>
                <select class="form-select" id="statusFilter">
                    <option value="">Tất cả</option>
                    <option value="active">Đang hoạt động</option>
                    <option value="inactive">Không hoạt động</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">Tìm kiếm</label>
                <input type="text" class="form-control" id="examSearch" placeholder="Tìm theo tên bài kiểm tra...">
            </div>
            <div class="col-md-3">
                <label class="form-label">Có lịch thi</label>
                <select class="form-select" id="scheduleFilter">
                    <option value="">Tất cả</option>
                    <option value="yes">Có lịch thi</option>
                    <option value="no">Chưa có lịch</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-primary-gradient w-100" id="applyFilter">
                    <i class="fas fa-search me-2"></i>Áp dụng
                </button>
            </div>
        </div>
    </div>
</div>

<div class="admin-action-card animate__animated animate__fadeInUp">
    <div class="card-header">
        <h5 class="mb-0 fw-bold">
            <i class="fas fa-list me-2"></i> Danh sách bài kiểm tra
        </h5>
    </div>
    <div class="card-body">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr style="background: linear-gradient(135deg, #a8e6cf, #7fcdcd); color: #2e7d32;">
                            <th class="border-0 fw-bold">Tiêu đề</th>
                            <th class="border-0 fw-bold">Mô tả</th>
                            <th class="border-0 fw-bold">File đính kèm</th>
                            <th class="border-0 fw-bold">Lịch thi</th>
                            <th class="border-0 fw-bold">Ngày tạo</th>
                            <th class="border-0 fw-bold text-center">Trạng thái</th>
                            <th class="border-0 fw-bold text-center">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var exam in Model)
                        {
                            <tr class="animate__animated animate__fadeIn" style="animation-delay: @(Model.ToList().IndexOf(exam) * 0.1)s;">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm me-2">
                                            <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                 style="width: 35px; height: 35px; background: linear-gradient(135deg, #89f7fe, #66a6ff);">
                                                <i class="fas fa-file-alt text-white"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="fw-bold text-primary">@exam.Title</div>
                                            <small class="text-muted">Bài kiểm tra</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if (!string.IsNullOrEmpty(exam.Description))
                                    {
                                        <span class="text-muted" title="@exam.Description">
                                            @(exam.Description.Length > 50 ? exam.Description.Substring(0, 50) + "..." : exam.Description)
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">Không có mô tả</span>
                                    }
                                </td>
                                <td class="text-center">
                                    @if (!string.IsNullOrEmpty(exam.FilePath))
                                    {
                                        <span class="badge bg-success rounded-pill px-3">
                                            <i class="fas fa-file me-1"></i>@exam.FileName
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary rounded-pill px-3">
                                            <i class="fas fa-file-slash me-1"></i>Chưa có file
                                        </span>
                                    }
                                </td>
                                <td class="text-center">
                                    @if (exam.ExamSchedules.Any())
                                    {
                                        var scheduleCount = exam.ExamSchedules.Count;
                                        var badgeColor = scheduleCount > 3 ? "success" : scheduleCount > 1 ? "primary" : "info";
                                        <span class="badge bg-@badgeColor rounded-pill px-3">
                                            <i class="fas fa-calendar me-1"></i>@scheduleCount lịch thi
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-warning rounded-pill px-3">
                                            <i class="fas fa-exclamation-triangle me-1"></i>Chưa có lịch
                                        </span>
                                    }
                                </td>
                                <td class="text-center">
                                    <small class="text-muted">@exam.CreatedAt.ToString("dd/MM/yyyy")</small>
                                    <br><small class="text-muted">@exam.CreatedAt.ToString("HH:mm")</small>
                                </td>
                                <td class="text-center">
                                    @if (exam.IsActive)
                                    {
                                        <span class="badge bg-success rounded-pill px-3">
                                            <i class="fas fa-check-circle me-1"></i>Hoạt động
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary rounded-pill px-3">
                                            <i class="fas fa-pause-circle me-1"></i>Không hoạt động
                                        </span>
                                    }
                                </td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <a href="@Url.Action("EditExam", new { id = exam.Id })"
                                           class="btn btn-sm btn-outline-primary rounded-pill me-1"
                                           data-bs-toggle="tooltip"
                                           title="Chỉnh sửa bài kiểm tra">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="@Url.Action("CreateSchedule", new { examId = exam.Id })"
                                           class="btn btn-sm btn-outline-success rounded-pill me-1"
                                           data-bs-toggle="tooltip"
                                           title="Tạo lịch thi">
                                            <i class="fas fa-calendar-plus"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-sm btn-outline-info rounded-pill me-1"
                                                data-bs-toggle="modal"
                                                data-bs-target="#<EMAIL>"
                                                title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        @if (!exam.ExamSchedules.Any())
                                        {
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-danger rounded-pill"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#<EMAIL>"
                                                    title="Xóa bài kiểm tra">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <div class="empty-state">
                    <div class="empty-state-icon mb-4">
                        <i class="fas fa-file-alt fa-4x" style="color: #a8e6cf;"></i>
                    </div>
                    <h4 class="text-muted mb-3">Chưa có bài kiểm tra nào</h4>
                    <p class="text-muted mb-4">Hãy tạo bài kiểm tra đầu tiên cho môn học của bạn</p>
                    <a href="@Url.Action("CreateExam")" class="btn btn-success-gradient btn-lg rounded-pill px-4">
                        <i class="fas fa-plus me-2"></i>Tạo bài kiểm tra mới
                    </a>
                </div>
            </div>
        }
    </div>
</div>

<!-- Enhanced Statistics Cards -->
<div class="container-fluid px-0 mt-4">
    <div class="row g-3 justify-content-center mx-auto" style="max-width: 1000px;">
        <!-- Total Exams Card -->
        <div class="col-lg-3 col-md-6">
            <div class="stat-card-modern stat-card-exams">
                <div class="stat-card-content">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon-bg stat-icon-bg-teal">
                            <i class="fas fa-file-alt"></i>
                        </div>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number">@Model.Count()</div>
                        <div class="stat-label">Tổng bài kiểm tra</div>
                        <div class="stat-description">Bài kiểm tra của bạn</div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <span class="stat-link">Chi tiết <i class="fas fa-arrow-right ms-1"></i></span>
                </div>
            </div>
        </div>

        <!-- Active Exams Card -->
        <div class="col-lg-3 col-md-6">
            <div class="stat-card-modern stat-card-students">
                <div class="stat-card-content">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon-bg stat-icon-bg-green">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number">@Model.Count(e => e.IsActive)</div>
                        <div class="stat-label">Đang hoạt động</div>
                        <div class="stat-description">Bài kiểm tra active</div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <span class="stat-link">Chi tiết <i class="fas fa-arrow-right ms-1"></i></span>
                </div>
            </div>
        </div>

        <!-- Exam Schedules Card -->
        <div class="col-lg-3 col-md-6">
            <div class="stat-card-modern stat-card-subjects">
                <div class="stat-card-content">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon-bg stat-icon-bg-orange">
                            <i class="fas fa-calendar"></i>
                        </div>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number">@Model.Sum(e => e.ExamSchedules.Count)</div>
                        <div class="stat-label">Lịch thi</div>
                        <div class="stat-description">Tổng số lịch thi</div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <span class="stat-link">Chi tiết <i class="fas fa-arrow-right ms-1"></i></span>
                </div>
            </div>
        </div>

        <!-- Files Card -->
        <div class="col-lg-3 col-md-6">
            <div class="stat-card-modern stat-card-classes">
                <div class="stat-card-content">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon-bg stat-icon-bg-purple">
                            <i class="fas fa-paperclip"></i>
                        </div>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number">@Model.Count(e => !string.IsNullOrEmpty(e.FilePath))</div>
                        <div class="stat-label">Có file đính kèm</div>
                        <div class="stat-description">Bài kiểm tra có file</div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <span class="stat-link">Chi tiết <i class="fas fa-arrow-right ms-1"></i></span>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .table tbody tr {
        border: none;
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background-color: rgba(168, 230, 207, 0.1) !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .empty-state {
        animation: fadeInUp 0.6s ease-out;
    }

    .empty-state-icon {
        animation: float 3s ease-in-out infinite;
    }

    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @@keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-10px);
        }
    }

    .avatar-sm {
        transition: all 0.3s ease;
    }

    .avatar-sm:hover {
        transform: scale(1.05);
    }

    .badge {
        transition: all 0.3s ease;
    }

    .badge:hover {
        transform: scale(1.05);
    }
</style>

@section Scripts {
<script>
    // Enhanced Teacher Exams Page Interactions
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize components
        initializeFilters();
        initializeSearch();
        initializeAnimations();
        initializeTooltips();
    });

    function initializeFilters() {
        const filterBtn = document.getElementById('filterBtn');
        const filterPanel = document.getElementById('filterPanel');
        const applyFilter = document.getElementById('applyFilter');

        if (filterBtn && filterPanel) {
            filterBtn.addEventListener('click', function() {
                if (filterPanel.style.display === 'none' || !filterPanel.style.display) {
                    filterPanel.style.display = 'block';
                    filterPanel.classList.add('animate__animated', 'animate__fadeInDown');
                    this.innerHTML = '<i class="fas fa-times me-2"></i>Đóng';
                } else {
                    filterPanel.style.display = 'none';
                    this.innerHTML = '<i class="fas fa-filter me-2"></i>Lọc';
                }
            });
        }

        if (applyFilter) {
            applyFilter.addEventListener('click', function() {
                applyFilters();
            });
        }
    }

    function applyFilters() {
        const statusFilter = document.getElementById('statusFilter').value;
        const scheduleFilter = document.getElementById('scheduleFilter').value;
        const searchTerm = document.getElementById('examSearch').value.toLowerCase();

        const tableRows = document.querySelectorAll('tbody tr');

        tableRows.forEach(row => {
            let showRow = true;

            // Status filter
            if (statusFilter) {
                const statusBadge = row.querySelector('td:nth-child(6) .badge');
                const isActive = statusBadge && statusBadge.textContent.includes('Hoạt động');
                if ((statusFilter === 'active' && !isActive) ||
                    (statusFilter === 'inactive' && isActive)) {
                    showRow = false;
                }
            }

            // Schedule filter
            if (scheduleFilter) {
                const scheduleBadge = row.querySelector('td:nth-child(4) .badge');
                const hasSchedule = scheduleBadge && !scheduleBadge.textContent.includes('Chưa có lịch');
                if ((scheduleFilter === 'yes' && !hasSchedule) ||
                    (scheduleFilter === 'no' && hasSchedule)) {
                    showRow = false;
                }
            }

            // Search filter
            if (searchTerm) {
                const examTitle = row.cells[0].textContent.toLowerCase();
                const examDescription = row.cells[1].textContent.toLowerCase();
                if (!examTitle.includes(searchTerm) && !examDescription.includes(searchTerm)) {
                    showRow = false;
                }
            }

            // Show/hide row
            if (showRow) {
                row.style.display = '';
                row.style.animation = 'fadeIn 0.3s ease-out';
            } else {
                row.style.display = 'none';
            }
        });
    }

    function initializeSearch() {
        const searchInput = document.getElementById('examSearch');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                applyFilters();
            });
        }
    }

    function initializeAnimations() {
        // Animate table rows
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach((row, index) => {
            row.style.animationDelay = (index * 0.05) + 's';
        });

        // Animate stat cards
        const statCards = document.querySelectorAll('.stat-card-modern');
        statCards.forEach((card, index) => {
            card.style.animationDelay = (index * 0.1 + 0.5) + 's';
            card.classList.add('animate__animated', 'animate__fadeInUp');
        });
    }

    function initializeTooltips() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @@keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .animate__fadeInDown {
            animation-duration: 0.5s;
        }
    `;
    document.head.appendChild(style);
</script>
}
</div> <!-- End teacher-page-container -->
