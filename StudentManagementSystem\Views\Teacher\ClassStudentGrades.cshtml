@model IEnumerable<StudentManagementSystem.Models.StudentGrade>
@{
    ViewData["Title"] = "Quản lý điểm số lớp học";
    var classItem = ViewBag.Class as StudentManagementSystem.Models.Class;
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-star"></i> Quản lý điểm số - @classItem.ClassName</h2>
    <div>
        <a href="@Url.Action("CreateStudentGrade", "Admin", new { classId = classItem.Id })" class="btn btn-success">
            <i class="fas fa-plus"></i> Thêm điểm mới
        </a>
        <a href="@Url.Action("ClassDetails", new { id = classItem.Id })" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>
</div>

@if (TempData["Success"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        @TempData["Success"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<!-- Class Info -->
<div class="card mb-4">
    <div class="card-header">
        <h5><i class="fas fa-info-circle"></i> Thông tin lớp học</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <strong>Lớp học:</strong> @classItem.ClassName
            </div>
            <div class="col-md-3">
                <strong>Học kỳ:</strong> @classItem.Semester - @classItem.Year
            </div>
            <div class="col-md-3">
                <strong>Môn học:</strong>
                @foreach (var cs in classItem.ClassSubjects)
                {
                    <span class="badge bg-primary me-1">@cs.Subject.SubjectName</span>
                }
            </div>
            <div class="col-md-3">
                <strong>Sinh viên:</strong> @classItem.ClassStudents.Count(cs => cs.IsActive)
            </div>
        </div>
    </div>
</div>

<!-- Grades by Subject -->
@foreach (var subject in classItem.ClassSubjects.Select(cs => cs.Subject))
{
    var subjectGrades = Model.Where(g => g.SubjectId == subject.Id).ToList();
    
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5><i class="fas fa-book"></i> @subject.SubjectName (@subject.SubjectCode)</h5>
            <div>
                <span class="badge bg-info">@subjectGrades.Count/@classItem.ClassStudents.Count(cs => cs.IsActive) sinh viên có điểm</span>
                <a href="@Url.Action("CreateStudentGrade", "Admin", new { classId = classItem.Id, subjectId = subject.Id })" class="btn btn-sm btn-success">
                    <i class="fas fa-plus"></i> Thêm điểm
                </a>
            </div>
        </div>
        <div class="card-body">
            @if (subjectGrades.Any())
            {
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Mã SV</th>
                                <th>Họ tên</th>
                                <th>Điểm danh (10%)</th>
                                <th>Giữa kỳ (30%)</th>
                                <th>Cuối kỳ (60%)</th>
                                <th>Điểm tổng kết</th>
                                <th>Xếp loại</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var grade in subjectGrades.OrderBy(g => g.Student.StudentId))
                            {
                                <tr>
                                    <td><strong>@grade.Student.StudentId</strong></td>
                                    <td>@grade.Student.FullName</td>
                                    <td>
                                        <span class="badge bg-info">@grade.AttendanceScore.ToString("F1")</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">@grade.MidtermScore.ToString("F1")</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">@grade.FinalScore.ToString("F1")</span>
                                    </td>
                                    <td>
                                        @{
                                            var totalScore = grade.AttendanceScore * 0.1m + grade.MidtermScore * 0.3m + grade.FinalScore * 0.6m;
                                            var badgeClass = totalScore >= 8.0m ? "bg-success" : 
                                                           totalScore >= 6.5m ? "bg-primary" : 
                                                           totalScore >= 5.0m ? "bg-warning" : "bg-danger";
                                        }
                                        <span class="badge @badgeClass fs-6">@totalScore.ToString("F1")</span>
                                    </td>
                                    <td>
                                        @{
                                            var letterGrade = totalScore >= 8.5m ? "A" :
                                                            totalScore >= 8.0m ? "B+" :
                                                            totalScore >= 7.0m ? "B" :
                                                            totalScore >= 6.5m ? "C+" :
                                                            totalScore >= 5.5m ? "C" :
                                                            totalScore >= 5.0m ? "D+" :
                                                            totalScore >= 4.0m ? "D" : "F";
                                        }
                                        <span class="badge @badgeClass">@letterGrade</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="@Url.Action("EditStudentGrade", "Admin", new { id = grade.Id })" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i> Sửa
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#<EMAIL>">
                                                <i class="fas fa-eye"></i> Xem
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
                
                <!-- Statistics -->
                <div class="row mt-3">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-primary">@subjectGrades.Average(g => g.AttendanceScore * 0.1m + g.MidtermScore * 0.3m + g.FinalScore * 0.6m).ToString("F1")</h6>
                            <small class="text-muted">Điểm trung bình</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-success">@subjectGrades.Count(g => (g.AttendanceScore * 0.1m + g.MidtermScore * 0.3m + g.FinalScore * 0.6m) >= 5.0m)</h6>
                            <small class="text-muted">Đạt</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-danger">@subjectGrades.Count(g => (g.AttendanceScore * 0.1m + g.MidtermScore * 0.3m + g.FinalScore * 0.6m) < 5.0m)</h6>
                            <small class="text-muted">Không đạt</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-info">@subjectGrades.Count(g => (g.AttendanceScore * 0.1m + g.MidtermScore * 0.3m + g.FinalScore * 0.6m) >= 8.0m)</h6>
                            <small class="text-muted">Giỏi</small>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="text-center py-4">
                    <i class="fas fa-star fa-2x text-muted mb-2"></i>
                    <p class="text-muted">Chưa có điểm nào cho môn học này</p>
                    <a href="@Url.Action("CreateStudentGrade", "Admin", new { classId = classItem.Id, subjectId = subject.Id })" class="btn btn-success">
                        <i class="fas fa-plus"></i> Thêm điểm đầu tiên
                    </a>
                </div>
            }
        </div>
    </div>
}

<!-- Missing Students Alert -->
@{
    var studentsWithoutGrades = classItem.ClassStudents
        .Where(cs => cs.IsActive && !Model.Any(g => g.StudentUserId == cs.StudentUserId))
        .ToList();
}

@if (studentsWithoutGrades.Any())
{
    <div class="alert alert-warning">
        <h6><i class="fas fa-exclamation-triangle"></i> Sinh viên chưa có điểm:</h6>
        <ul class="mb-0">
            @foreach (var student in studentsWithoutGrades)
            {
                <li>@student.Student.FullName (@student.Student.StudentId)</li>
            }
        </ul>
        <div class="mt-2">
            <a href="@Url.Action("CreateStudentGrade", "Admin", new { classId = classItem.Id })" class="btn btn-sm btn-warning">
                <i class="fas fa-plus"></i> Thêm điểm cho sinh viên này
            </a>
        </div>
    </div>
}
