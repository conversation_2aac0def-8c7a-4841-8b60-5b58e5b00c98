@startuml Teacher_Exam_System
!theme plain
title Teacher - <PERSON><PERSON> thống Thi trực tuyến và Chấm điểm Tự động

actor Teacher
participant "TeacherController" as TC
participant "ApplicationDbContext" as DB
participant "AutoGradingService" as AGS
participant "UserManager" as UM

== Tạo Bài thi ==
Teacher -> TC: GET /Teacher/CreateExam
activate TC
TC -> UM: GetUserAsync(User)
activate UM
UM --> TC: Current Teacher
deactivate UM

TC -> DB: Classes.Where(TeacherUserId == currentUser.Id)
activate DB
DB --> TC: Teacher's Classes
deactivate DB

TC --> Teacher: CreateExamViewModel with Classes
deactivate TC

Teacher -> TC: POST /Teacher/CreateExam(CreateExamViewModel)
activate TC

TC -> DB: Exams.Add(newExam)
activate DB

loop for each question
    TC -> DB: Questions.Add(newQuestion)
    loop for each answer option
        TC -> DB: AnswerOptions.Add(newOption)
    end
end

TC -> DB: SaveChangesAsync()
DB --> TC: Exam Created Successfully
deactivate DB

TC --> Teacher: Redirect to Exams List + Success Message
deactivate TC

== Tạo Lịch thi ==
Teacher -> TC: POST /Teacher/CreateSchedule
activate TC

TC -> DB: ExamSchedules.Add(newSchedule)
activate DB
TC -> DB: SaveChangesAsync()
DB --> TC: Schedule Created
deactivate DB

TC --> Teacher: Schedule Created Successfully
deactivate TC

== Chấm điểm Tự động ==
Teacher -> TC: POST /Teacher/ProcessExpiredExam/{examScheduleId}
activate TC

TC -> DB: ExamSchedules.Include(Submissions).FirstOrDefaultAsync(id)
activate DB
DB --> TC: ExamSchedule with Submissions
deactivate DB

TC -> AGS: ProcessAutoGrading(examSchedule)
activate AGS

loop for each submission without grade
    AGS -> AGS: CalculateMultipleChoiceScore()
    AGS -> DB: Grades.Add(newGrade)
    activate DB
    DB --> AGS: Grade Saved
    deactivate DB
end

AGS --> TC: Auto Grading Completed
deactivate AGS

TC --> Teacher: Processing Complete + Results Summary
deactivate TC

== Chấm điểm Thủ công ==
Teacher -> TC: GET /Teacher/GradeSubmission/{submissionId}
activate TC

TC -> DB: Submissions.Include(Student, ExamSchedule.Exam).FirstOrDefaultAsync(id)
activate DB
DB --> TC: Submission Details
deactivate DB

TC --> Teacher: GradeSubmissionViewModel
deactivate TC

Teacher -> TC: POST /Teacher/GradeSubmission
activate TC

TC -> DB: Grades.Add(manualGrade) or Update existing
activate DB
TC -> DB: SaveChangesAsync()
DB --> TC: Grade Saved
deactivate DB

TC --> Teacher: Grading Completed + Success Message
deactivate TC

@enduml