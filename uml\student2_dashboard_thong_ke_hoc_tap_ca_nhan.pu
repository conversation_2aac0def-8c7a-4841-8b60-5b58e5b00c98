@startuml Student_Dashboard_Analytics
!theme plain
title Student - Dashboard Thống kê Học tập C<PERSON> nhân

actor Student
participant "StudentController" as SC
participant "ApplicationDbContext" as DB
participant "UserManager" as UM
participant "GradeCalculationService" as GCS
participant "NotificationService" as NS

== Load Dashboard ==
Student -> SC: GET /Student/Index
activate SC

SC -> UM: GetUserAsync(User)
activate UM
UM --> SC: Current Student
deactivate UM

== Tính toán Thống kê Real-time ==
SC -> DB: StudentGrades.Include(Subject).Where(StudentUserId)
activate DB
DB --> SC: Student's Grades
deactivate DB

SC -> GCS: CalculateGPA(studentGrades)
activate GCS
GCS -> GCS: totalPoints = Sum(gradePoint * credits)
GCS -> GCS: totalCredits = Sum(credits)
GCS -> GCS: gpa = totalPoints / totalCredits
GCS --> SC: GPA (decimal)
deactivate GCS

SC -> GCS: GetFailedSubjectsCount(studentGrades)
activate GCS
GCS -> GCS: Count grades where TotalScore < 5.0
GCS --> SC: Failed Subjects Count
deactivate GCS

== Kiểm tra Cảnh báo <PERSON>c tập ==
alt Failed Subjects >= 2
    SC -> NS: CreateAcademicWarning(student, failedCount)
    activate NS
    NS --> SC: Warning Message
    deactivate NS
    SC -> SC: TempData["AcademicWarning"] = message
end

== Lấy Bài thi Có sẵn ==
SC -> DB: ExamSchedules.Where(now >= StartTime && now <= EndTime && IsActive).Count()
activate DB
DB --> SC: Available Exams Count
deactivate DB

== Lấy Bài đã Hoàn thành ==
SC -> DB: Submissions.Where(StudentUserId == currentUser.Id).Count()
activate DB
DB --> SC: Completed Exams Count
deactivate DB

== Lấy Kết quả Chờ ==
SC -> DB: Submissions.Where(StudentUserId && Grade == null).Count()
activate DB
DB --> SC: Pending Results Count
deactivate DB

== Lấy Hoạt động Gần đây ==
SC -> SC: GetRecentActivitiesAsync(studentId)
activate SC
SC -> DB: Recent Submissions, Grades, Exam Schedules
activate DB
DB --> SC: Recent Activities Data
deactivate DB
SC --> SC: Formatted Activities List
deactivate SC

== Lấy Deadline Sắp tới ==
SC -> SC: GetUpcomingDeadlinesAsync(studentId)
activate SC
SC -> DB: ExamSchedules.Where(StartTime > now).OrderBy(StartTime).Take(5)
activate DB
DB --> SC: Upcoming Deadlines
deactivate DB
SC --> SC: Formatted Deadlines List
deactivate SC

== Tạo Dashboard ViewModel ==
SC -> SC: Create StudentDashboardViewModel
SC -> SC: Set all calculated statistics
SC -> SC: Set recent activities and deadlines

SC --> Student: Dashboard with Complete Analytics
deactivate SC

== Real-time Updates (Optional) ==
note over Student, SC
    Dashboard có thể được cập nhật real-time
    thông qua SignalR hoặc AJAX polling
    để hiển thị thông tin mới nhất
end note

@enduml