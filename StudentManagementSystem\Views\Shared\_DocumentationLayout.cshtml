<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Student Management System</title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="~/lib/bootstrap/dist/css/bootstrap.min.css" as="style" />
    <link rel="preload" href="~/css/design-system.css" as="style" />
    <link rel="preload" href="~/css/documentation.css" as="style" />
    
    <!-- DNS prefetch for external resources -->
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com" />
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />

    <!-- Core CSS -->
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/design-system.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/documentation.css" asp-append-version="true" />
    
    <!-- External CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    
    @await RenderSectionAsync("Styles", required: false)
</head>
<body>
    <!-- Skip Links for Accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <a href="#navigation" class="skip-link">Skip to navigation</a>
    
    <header class="doc-nav" role="banner">
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm" role="navigation" id="navigation" aria-label="Documentation navigation">
            <div class="container-fluid">
                <a class="navbar-brand" href="@Url.Action("Index", "Home")">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-graduation-cap me-2 text-primary"></i>
                        <span class="fw-bold">SMS Documentation</span>
                    </div>
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                        aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="@Url.Action("DesignSystem", "Documentation")">
                                <i class="fas fa-palette me-1"></i>Design System
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="@Url.Action("UserExperience", "Documentation")">
                                <i class="fas fa-user-friends me-1"></i>UX Guidelines
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="@Url.Action("Components", "Documentation")">
                                <i class="fas fa-cubes me-1"></i>Components
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="@Url.Action("Deployment", "Documentation")">
                                <i class="fas fa-rocket me-1"></i>Deployment
                            </a>
                        </li>
                    </ul>
                    
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="@Url.Action("Index", "Home")">
                                <i class="fas fa-home me-1"></i>Back to App
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content Area -->
    <main class="main-content" role="main" id="main-content" aria-label="Documentation content">
        @RenderBody()
    </main>

    <!-- Footer -->
    <footer class="doc-footer bg-light mt-5">
        <div class="container-fluid">
            <div class="row py-4">
                <div class="col-md-6">
                    <h5 class="fw-bold text-primary">Student Management System</h5>
                    <p class="text-muted mb-0">Comprehensive documentation for developers and designers.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="d-flex justify-content-md-end gap-3">
                        <a href="#" class="text-muted text-decoration-none">
                            <i class="fab fa-github me-1"></i>GitHub
                        </a>
                        <a href="#" class="text-muted text-decoration-none">
                            <i class="fas fa-book me-1"></i>API Docs
                        </a>
                        <a href="#" class="text-muted text-decoration-none">
                            <i class="fas fa-question-circle me-1"></i>Support
                        </a>
                    </div>
                </div>
            </div>
            <hr>
            <div class="row py-2">
                <div class="col-12 text-center">
                    <small class="text-muted">
                        © 2024 Student Management System. Built with ❤️ for education.
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Core JavaScript -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Documentation specific JavaScript -->
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Highlight current section in navigation
        function highlightCurrentSection() {
            const sections = document.querySelectorAll('.doc-section');
            const navLinks = document.querySelectorAll('.doc-toc a');
            
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (window.scrollY >= sectionTop - 100) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        }

        // Add scroll event listener
        window.addEventListener('scroll', highlightCurrentSection);
        
        // Copy code functionality
        document.querySelectorAll('pre code').forEach(block => {
            const button = document.createElement('button');
            button.className = 'btn btn-sm btn-outline-secondary copy-btn';
            button.innerHTML = '<i class="fas fa-copy"></i>';
            button.style.position = 'absolute';
            button.style.top = '0.5rem';
            button.style.right = '0.5rem';
            
            const pre = block.parentElement;
            pre.style.position = 'relative';
            pre.appendChild(button);
            
            button.addEventListener('click', () => {
                navigator.clipboard.writeText(block.textContent).then(() => {
                    button.innerHTML = '<i class="fas fa-check"></i>';
                    setTimeout(() => {
                        button.innerHTML = '<i class="fas fa-copy"></i>';
                    }, 2000);
                });
            });
        });
    </script>
    
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
