@model StudentManagementSystem.ViewModels.StudentDashboardViewModel
@{
    ViewData["Title"] = "Dashboard - Học sinh";
}

<div class="student-page-container">
<div class="student-dashboard-container">
<!-- Welcome Header with Animation -->
<div class="welcome-header mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <div class="position-relative z-index-2">
            <h2 class="mb-2 animate__animated animate__fadeInLeft fw-bold">
                <i class="fas fa-leaf me-2"></i>
                Chào mừng trở lại! 👋
            </h2>
            <p class="mb-0 opacity-75">Hôm nay là ngày tuyệt vời để học tập và khám phá kiến thức mới</p>
        </div>
        <div class="text-end position-relative z-index-2">
            <div class="badge bg-white text-success fs-6 mb-2 px-3 py-2 rounded-pill">
                <i class="fas fa-calendar me-1"></i> @DateTime.Now.ToString("dd/MM/yyyy")
            </div>
            <br>
            <small class="opacity-75">
                <i class="fas fa-clock me-1"></i> @DateTime.Now.ToString("HH:mm")
            </small>
        </div>
    </div>
</div>

<!-- Quick Actions Bar -->
<div class="quick-actions mb-4">
    <div class="row g-2">
        <div class="col-6 col-md-3">
            <a href="@Url.Action("AvailableExams")" class="btn btn-outline-primary w-100 quick-action-btn">
                <i class="fas fa-file-alt fa-lg mb-1"></i>
                <br><small>Bài thi</small>
            </a>
        </div>
        <div class="col-6 col-md-3">
            <a href="@Url.Action("MySchedule")" class="btn btn-outline-info w-100 quick-action-btn">
                <i class="fas fa-calendar-alt fa-lg mb-1"></i>
                <br><small>Lịch học</small>
            </a>
        </div>
        <div class="col-6 col-md-3">
            <a href="@Url.Action("MyGrades")" class="btn btn-outline-success w-100 quick-action-btn">
                <i class="fas fa-star fa-lg mb-1"></i>
                <br><small>Điểm số</small>
            </a>
        </div>
        <div class="col-6 col-md-3">
            <a href="@Url.Action("MySubmissions")" class="btn btn-outline-warning w-100 quick-action-btn">
                <i class="fas fa-paper-plane fa-lg mb-1"></i>
                <br><small>Bài đã nộp</small>
            </a>
        </div>
    </div>
</div>

@if (TempData["AcademicWarning"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle"></i> <strong>Cảnh báo học tập!</strong><br>
        @TempData["AcademicWarning"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<!-- Enhanced Statistics Cards -->
<div class="container-fluid px-0">
    <div class="row mb-4 g-3 justify-content-center mx-auto" style="max-width: 1200px;">
    <!-- Failed Subjects Card -->
    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
        <div class="stat-card-simple stat-card-danger">
            <div class="stat-card-header bg-danger"></div>
            <div class="stat-card-body">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-number">@Model.FailedSubjectsCount</div>
                <div class="stat-label">MÔN RỚT</div>
                @if (Model.HasAcademicWarning)
                {
                    <div class="stat-warning">
                        <i class="fas fa-warning"></i> Cảnh báo học tập
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Available Exams Card -->
    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
        <a href="@Url.Action("AvailableExams")" class="text-decoration-none">
            <div class="stat-card-simple stat-card-primary">
                <div class="stat-card-header bg-primary"></div>
                <div class="stat-card-body">
                    <div class="stat-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-number">@Model.AvailableExams</div>
                    <div class="stat-label">BÀI THI CÓ SẴN</div>
                </div>
            </div>
        </a>
    </div>

    <!-- Completed Exams Card -->
    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
        <a href="@Url.Action("MySubmissions")" class="text-decoration-none">
            <div class="stat-card-simple stat-card-success">
                <div class="stat-card-header bg-success"></div>
                <div class="stat-card-body">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-number">@Model.CompletedExams</div>
                    <div class="stat-label">ĐÃ HOÀN THÀNH</div>
                </div>
            </div>
        </a>
    </div>

    <!-- Pending Results Card -->
    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
        <a href="@Url.Action("MySubmissions")" class="text-decoration-none">
            <div class="stat-card-simple stat-card-warning">
                <div class="stat-card-header bg-warning"></div>
                <div class="stat-card-body">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-number">@Model.PendingResults</div>
                    <div class="stat-label">CHỜ KẾT QUẢ</div>
                </div>
            </div>
        </a>
    </div>

    <!-- GPA Card -->
    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
        <a href="@Url.Action("MyGrades")" class="text-decoration-none">
            <div class="stat-card-simple stat-card-info">
                <div class="stat-card-header bg-info"></div>
                <div class="stat-card-body">
                    <div class="stat-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-number">@Model.GPA.ToString("F2")</div>
                    <div class="stat-label">GPA (THANG ĐIỂM 4)</div>
                </div>
            </div>
        </a>
    </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tasks"></i> Hoạt động</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="@Url.Action("MySchedule")" class="btn btn-info btn-lg">
                        <i class="fas fa-calendar-alt"></i> Xem lịch học
                        @if (Model.AvailableExams > 0)
                        {
                            <span class="badge bg-light text-info ms-2">@Model.AvailableExams</span>
                        }
                    </a>
                    <a href="@Url.Action("AvailableExams")" class="btn btn-primary btn-lg">
                        <i class="fas fa-file-alt"></i> Xem bài thi có sẵn
                        @if (Model.AvailableExams > 0)
                        {
                            <span class="badge bg-light text-primary ms-2">@Model.AvailableExams</span>
                        }
                    </a>
                    <a href="@Url.Action("MySubmissions")" class="btn btn-success btn-lg">
                        <i class="fas fa-paper-plane"></i> Bài đã nộp
                        @if (Model.CompletedExams > 0)
                        {
                            <span class="badge bg-light text-success ms-2">@Model.CompletedExams</span>
                        }
                    </a>
                    <a href="@Url.Action("MyGrades")" class="btn btn-warning btn-lg">
                        <i class="fas fa-star"></i> Xem điểm số
                        <span class="badge bg-light text-warning ms-2">GPA: @Model.GPA.ToString("F1")</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-line"></i> Kết quả</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="@Url.Action("MyGrades")" class="btn btn-info">
                        <i class="fas fa-star"></i> Xem điểm số
                    </a>
                    @if (Model.PendingResults > 0)
                    {
                        <div class="alert alert-warning mb-0">
                            <i class="fas fa-exclamation-triangle"></i>
                            Bạn có @Model.PendingResults bài thi chờ kết quả
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity & Quick Stats -->
<div class="row mt-4">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white border-0 pb-0">
                <h5 class="mb-0 text-success">
                    <i class="fas fa-clock me-2"></i> Hoạt động gần đây
                </h5>
            </div>
            <div class="card-body">
                @if (Model.RecentActivities.Any())
                {
                    <div class="timeline">
                        @foreach (var activity in Model.RecentActivities)
                        {
                            <div class="timeline-item">
                                <div class="timeline-marker <EMAIL>"></div>
                                <div class="timeline-content">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1 fw-semibold">
                                                <i class="@activity.Icon me-1"></i>
                                                @activity.Title
                                            </h6>
                                            <p class="text-muted mb-1 small">@activity.Description</p>
                                        </div>
                                        <span class="badge bg-light text-muted small">@activity.TimeAgo</span>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center py-4">
                        <i class="fas fa-history fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">Chưa có hoạt động nào gần đây</p>
                    </div>
                }
                <div class="text-center mt-3">
                    <a href="@Url.Action("MySubmissions")" class="btn btn-outline-success btn-sm rounded-pill">
                        <i class="fas fa-history me-1"></i> Xem tất cả hoạt động
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Quick Stats -->
        <div class="card border-0 shadow-sm mb-3">
            <div class="card-header bg-white border-0 pb-0">
                <h6 class="mb-0 text-success">
                    <i class="fas fa-chart-pie me-2"></i> Thống kê nhanh
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="small">Tỷ lệ hoàn thành:</span>
                    <strong class="text-success">
                        @{
                            var completionRateQuickStats = Model.AvailableExams + Model.CompletedExams > 0 ?
                                (double)Model.CompletedExams / (Model.AvailableExams + Model.CompletedExams) * 100 : 0;
                        }
                        @completionRateQuickStats.ToString("F0")%
                    </strong>
                </div>
                <div class="progress mb-3" style="height: 6px;">
                    <div class="progress-bar bg-gradient bg-success" style="width: @completionRateQuickStats%"></div>
                </div>

                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="small">Điểm trung bình:</span>
                    <strong class="text-@(Model.AverageScore >= 8 ? "success" : Model.AverageScore >= 6.5 ? "info" : "warning")">
                        @Model.AverageScore.ToString("F1")/10
                    </strong>
                </div>
                <div class="progress mb-3" style="height: 6px;">
                    <div class="progress-bar bg-gradient bg-@(Model.AverageScore >= 8 ? "success" : Model.AverageScore >= 6.5 ? "info" : "warning")"
                         style="width: @(Model.AverageScore * 10)%"></div>
                </div>

                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="small">GPA hiện tại:</span>
                    <strong class="text-@(Model.GPA >= 3.5m ? "success" : Model.GPA >= 2.5m ? "info" : "warning")">
                        @Model.GPA.ToString("F2")/4.0
                    </strong>
                </div>
                <div class="progress mb-3" style="height: 6px;">
                    <div class="progress-bar bg-gradient bg-@(Model.GPA >= 3.5m ? "success" : Model.GPA >= 2.5m ? "info" : "warning")"
                         style="width: @(Model.GPA * 25)%"></div>
                </div>

                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-trophy me-1"></i>
                        @if (Model.GPA >= 3.5m)
                        {
                            <span class="text-success">Học lực Giỏi</span>
                        }
                        else if (Model.GPA >= 2.5m)
                        {
                            <span class="text-info">Học lực Khá</span>
                        }
                        else if (Model.GPA >= 2.0m)
                        {
                            <span class="text-warning">Học lực Trung bình</span>
                        }
                        else
                        {
                            <span class="text-danger">Cần cố gắng hơn</span>
                        }
                    </small>
                </div>
            </div>
        </div>

        <!-- Upcoming Deadlines -->
        <div class="card border-0 shadow-sm deadline-card">
            <div class="card-header bg-white border-0 pb-0">
                <h6 class="mb-0 text-success">
                    <i class="fas fa-clock me-2"></i> Deadline sắp tới
                </h6>
            </div>
            <div class="card-body">
                @if (Model.UpcomingDeadlines.Any())
                {
                    @foreach (var deadline in Model.UpcomingDeadlines.Take(3))
                    {
                        <div class="deadline-item mb-3 p-3 rounded bg-light">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fw-semibold">@deadline.Title</h6>
                                    <small class="text-muted">
                                        <i class="fas fa-book me-1"></i>@deadline.Subject
                                    </small>
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>@deadline.DueDate.ToString("dd/MM/yyyy HH:mm")
                                    </small>
                                </div>
                                <span class="badge <EMAIL> ms-2">
                                    @deadline.TimeRemaining
                                </span>
                            </div>
                        </div>
                    }
                }
                else
                {
                    <div class="text-center py-3">
                        <i class="fas fa-calendar-check fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0 small">Không có deadline sắp tới</p>
                    </div>
                }

                <div class="text-center mt-3">
                    <a href="@Url.Action("AvailableExams")" class="btn btn-outline-success btn-sm rounded-pill">
                        <i class="fas fa-calendar-check me-1"></i> Xem tất cả
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Floating Action Button -->
<button class="fab" id="fabMain" title="Menu nhanh">
    <i class="fas fa-plus"></i>
</button>

<!-- FAB Menu (Hidden by default) -->
<div class="fab-menu" id="fabMenu" style="display: none;">
    <a href="@Url.Action("AvailableExams")" class="fab-item" title="Bài thi mới">
        <i class="fas fa-file-alt"></i>
    </a>
    <a href="@Url.Action("MySchedule")" class="fab-item" title="Lịch học">
        <i class="fas fa-calendar"></i>
    </a>
    <a href="@Url.Action("MyGrades")" class="fab-item" title="Điểm số">
        <i class="fas fa-star"></i>
    </a>
</div>

<script>
// FAB Menu Toggle
document.getElementById('fabMain').addEventListener('click', function() {
    const fabMenu = document.getElementById('fabMenu');
    const fabMain = document.getElementById('fabMain');
    const icon = fabMain.querySelector('i');

    if (fabMenu.style.display === 'none') {
        fabMenu.style.display = 'block';
        icon.className = 'fas fa-times';
        fabMain.style.transform = 'rotate(45deg)';
    } else {
        fabMenu.style.display = 'none';
        icon.className = 'fas fa-plus';
        fabMain.style.transform = 'rotate(0deg)';
    }
});

// Animate progress circles
function animateProgressCircles() {
    document.querySelectorAll('.progress-circle').forEach(function(circle) {
        const percentage = parseFloat(circle.getAttribute('data-percentage')) || 0;
        const degrees = (percentage / 100) * 360;

        // Animate the conic gradient
        setTimeout(() => {
            circle.style.background = `conic-gradient(#81c784 ${degrees}deg, #e8f5e8 ${degrees}deg)`;
        }, Math.random() * 500);
    });
}

// Add animation to cards on scroll
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.animationDelay = Math.random() * 0.3 + 's';
            entry.target.classList.add('animate__animated', 'animate__fadeInUp');
        }
    });
}, observerOptions);

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Animate progress circles
    animateProgressCircles();

    // Observe all cards for scroll animations
    document.querySelectorAll('.card, .stat-card-modern').forEach(card => {
        observer.observe(card);
    });

    // Add stagger animation to stat cards
    document.querySelectorAll('.stat-card-modern').forEach((card, index) => {
        card.style.animationDelay = (index * 0.1) + 's';
        card.classList.add('animate__animated', 'animate__fadeInUp');
    });
});

// Show welcome toast
setTimeout(() => {
    if (typeof StudentUI !== 'undefined') {
        StudentUI.showToast('Chào mừng bạn đến với hệ thống!', 'info', 3000);
    }
}, 1000);
</script>
</div> <!-- End student-dashboard-container -->
</div> <!-- End student-page-container -->
