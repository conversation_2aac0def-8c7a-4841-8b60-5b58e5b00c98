@model StudentManagementSystem.ViewModels.EditAttendanceSessionViewModel
@{
    ViewData["Title"] = "Chỉnh sửa Buổi học";
}

@section Styles {
    <link href="~/css/student-ui.css" rel="stylesheet" />
    <link href="~/css/teacher-ui.css" rel="stylesheet" />
    <style>
        .form-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(168, 230, 207, 0.3);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .form-header {
            background: linear-gradient(135deg, #a8e6cf 0%, #7fcdcd 100%);
            color: #2d5016;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            font-weight: 600;
            color: #2d5016;
            margin-bottom: 0.5rem;
        }
        
        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #81c784;
            box-shadow: 0 0 0 0.2rem rgba(129, 199, 132, 0.25);
        }
        
        .btn-custom {
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
        }
        
        .validation-summary {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .session-info {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
    </style>
}

<!-- Welcome Header -->
<div class="welcome-header mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h2 class="mb-2 fw-bold">
                <i class="fas fa-edit me-2"></i>
                Chỉnh sửa Buổi học
            </h2>
            <p class="mb-0 opacity-75">Cập nhật thông tin buổi học</p>
        </div>
        <div>
            <a href="@Url.Action("Sessions")" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Quay lại
            </a>
        </div>
    </div>
</div>

<!-- Session Info -->
<div class="session-info">
    <div class="row">
        <div class="col-md-6">
            <h6 class="fw-bold text-primary">Thông tin lớp học</h6>
            <p class="mb-1"><strong>Lớp:</strong> @Model.ClassName</p>
            <p class="mb-0"><strong>Trạng thái:</strong> 
                <span class="badge @(Model.IsCompleted ? "bg-success" : "bg-warning")">
                    @(Model.IsCompleted ? "Đã hoàn thành" : "Chưa hoàn thành")
                </span>
            </p>
        </div>
        <div class="col-md-6">
            <h6 class="fw-bold text-info">Lưu ý</h6>
            <ul class="small mb-0">
                <li>Không thể thay đổi lớp học sau khi tạo</li>
                <li>Giờ kết thúc phải sau giờ bắt đầu</li>
                <li>Không thể tạo buổi học trong quá khứ</li>
            </ul>
        </div>
    </div>
</div>

<!-- Form -->
<div class="form-card">
    <form asp-action="EditAttendanceSession" method="post">
        @Html.AntiForgeryToken()
        <input type="hidden" asp-for="Id" />
        <input type="hidden" asp-for="ClassId" />
        <input type="hidden" asp-for="IsCompleted" />
        <input type="hidden" asp-for="ClassName" />

        <!-- Validation Summary -->
        @if (!ViewData.ModelState.IsValid)
        {
            <div class="validation-summary">
                <h6 class="text-danger mb-2">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Vui lòng kiểm tra lại thông tin:
                </h6>
                <div asp-validation-summary="All" class="text-danger"></div>
            </div>
        }

        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label asp-for="SessionTitle" class="form-label">
                        <i class="fas fa-heading me-2"></i>Tiêu đề buổi học *
                    </label>
                    <input asp-for="SessionTitle" class="form-control" placeholder="Nhập tiêu đề buổi học..." />
                    <span asp-validation-for="SessionTitle" class="text-danger small"></span>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label asp-for="Description" class="form-label">
                        <i class="fas fa-align-left me-2"></i>Mô tả nội dung
                    </label>
                    <textarea asp-for="Description" class="form-control" rows="3" 
                              placeholder="Mô tả nội dung buổi học (tùy chọn)..."></textarea>
                    <span asp-validation-for="Description" class="text-danger small"></span>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label asp-for="SessionDate" class="form-label">
                        <i class="fas fa-calendar me-2"></i>Ngày học *
                    </label>
                    <input asp-for="SessionDate" type="date" class="form-control" />
                    <span asp-validation-for="SessionDate" class="text-danger small"></span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label asp-for="StartTime" class="form-label">
                        <i class="fas fa-clock me-2"></i>Giờ bắt đầu *
                    </label>
                    <input asp-for="StartTime" type="time" class="form-control" />
                    <span asp-validation-for="StartTime" class="text-danger small"></span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label asp-for="EndTime" class="form-label">
                        <i class="fas fa-clock me-2"></i>Giờ kết thúc *
                    </label>
                    <input asp-for="EndTime" type="time" class="form-control" />
                    <span asp-validation-for="EndTime" class="text-danger small"></span>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label asp-for="Location" class="form-label">
                        <i class="fas fa-map-marker-alt me-2"></i>Phòng học
                    </label>
                    <input asp-for="Location" class="form-control" placeholder="Nhập phòng học (tùy chọn)..." />
                    <span asp-validation-for="Location" class="text-danger small"></span>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-between">
                    <a href="@Url.Action("Sessions")" class="btn btn-outline-secondary btn-custom">
                        <i class="fas fa-times me-2"></i>Hủy
                    </a>
                    <button type="submit" class="btn btn-success btn-custom">
                        <i class="fas fa-save me-2"></i>Cập nhật buổi học
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Auto-focus on first input
            $('#SessionTitle').focus();
            
            // Validate time inputs
            $('#StartTime, #EndTime').on('change', function() {
                var startTime = $('#StartTime').val();
                var endTime = $('#EndTime').val();
                
                if (startTime && endTime && startTime >= endTime) {
                    alert('Giờ kết thúc phải sau giờ bắt đầu!');
                    $(this).focus();
                }
            });
            
            // Validate date input
            $('#SessionDate').on('change', function() {
                var selectedDate = new Date($(this).val());
                var today = new Date();
                today.setHours(0, 0, 0, 0);
                
                if (selectedDate < today) {
                    alert('Không thể chọn ngày trong quá khứ!');
                    $(this).focus();
                }
            });
        });
    </script>
}
