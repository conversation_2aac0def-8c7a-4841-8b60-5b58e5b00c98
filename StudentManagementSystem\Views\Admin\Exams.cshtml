@model IEnumerable<StudentManagementSystem.Models.Exam>
@{
    ViewData["Title"] = "Tất cả bài kiểm tra";
}

@section Styles {
    <link href="~/css/admin-ui.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet" />
}

<!-- Welcome Header with Animation -->
<div class="welcome-header mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <div class="position-relative z-index-2">
            <h2 class="mb-2 animate__animated animate__fadeInLeft fw-bold">
                <i class="fas fa-file-alt me-2"></i>
                Tất cả bài kiểm tra 📝
            </h2>
            <p class="mb-0 opacity-75">Xem và quản lý tất cả bài kiểm tra trong hệ thống</p>
        </div>
        <div class="text-end position-relative z-index-2">
            <div class="d-flex gap-2">
                <button class="btn btn-outline-light rounded-pill px-3" id="filterBtn">
                    <i class="fas fa-filter me-2"></i>Lọc
                </button>
                <button class="btn btn-outline-light rounded-pill px-3" id="exportBtn">
                    <i class="fas fa-download me-2"></i>Xuất
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Filter Panel -->
<div class="admin-action-card mb-4" id="filterPanel" style="display: none;">
    <div class="card-header">
        <h6 class="mb-0 fw-bold">
            <i class="fas fa-filter me-2"></i>Bộ lọc bài kiểm tra
        </h6>
    </div>
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Trạng thái</label>
                <select class="form-select" id="statusFilter">
                    <option value="">Tất cả</option>
                    <option value="active">Đang hoạt động</option>
                    <option value="inactive">Không hoạt động</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Loại bài thi</label>
                <select class="form-select" id="typeFilter">
                    <option value="">Tất cả</option>
                    <option value="Giữa kỳ">Giữa kỳ</option>
                    <option value="Cuối kỳ">Cuối kỳ</option>
                    <option value="Thường xuyên">Thường xuyên</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">Tìm kiếm</label>
                <input type="text" class="form-control" id="examSearch" placeholder="Tìm theo tên bài kiểm tra...">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-primary-gradient w-100" id="applyFilter">
                    <i class="fas fa-search me-2"></i>Áp dụng
                </button>
            </div>
        </div>
    </div>
</div>

<div class="admin-action-card animate__animated animate__fadeInUp">
    <div class="card-header">
        <h5 class="mb-0 fw-bold">
            <i class="fas fa-list me-2"></i> Danh sách bài kiểm tra
        </h5>
    </div>
    <div class="card-body">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr style="background: linear-gradient(135deg, #a8e6cf, #7fcdcd); color: #2e7d32;">
                            <th class="border-0 fw-bold">Tên bài kiểm tra</th>
                            <th class="border-0 fw-bold">Mô tả</th>
                            <th class="border-0 fw-bold">Thời gian</th>
                            <th class="border-0 fw-bold">Loại bài thi</th>
                            <th class="border-0 fw-bold">Lịch thi</th>
                            <th class="border-0 fw-bold">Người tạo</th>
                            <th class="border-0 fw-bold">Ngày tạo</th>
                            <th class="border-0 fw-bold text-center">Trạng thái</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var exam in Model)
                        {
                            <tr class="animate__animated animate__fadeIn" style="animation-delay: @(Model.ToList().IndexOf(exam) * 0.1)s;">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm me-2">
                                            <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                 style="width: 35px; height: 35px; background: linear-gradient(135deg, #89f7fe, #66a6ff);">
                                                <i class="fas fa-file-alt text-white"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="fw-bold text-primary">@exam.Title</div>
                                            <small class="text-muted">Bài kiểm tra</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if (!string.IsNullOrEmpty(exam.Description))
                                    {
                                        <span class="text-muted" title="@exam.Description">
                                            @(exam.Description.Length > 50 ? exam.Description.Substring(0, 50) + "..." : exam.Description)
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">Không có mô tả</span>
                                    }
                                </td>
                                <td class="text-center">
                                    @if (exam.ExamSchedules.Any())
                                    {
                                        <span class="badge bg-info rounded-pill px-3">
                                            <i class="fas fa-clock me-1"></i>@exam.ExamSchedules.First().DurationMinutes phút
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Chưa có lịch</span>
                                    }
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-secondary rounded-pill px-3">
                                        <i class="fas fa-file me-1"></i>File bài thi
                                    </span>
                                </td>
                                <td class="text-center">
                                    @if (exam.ExamSchedules.Any())
                                    {
                                        var scheduleCount = exam.ExamSchedules.Count;
                                        var badgeColor = scheduleCount > 3 ? "success" : scheduleCount > 1 ? "primary" : "warning";
                                        <span class="badge bg-@badgeColor rounded-pill px-3">
                                            <i class="fas fa-calendar me-1"></i>@scheduleCount lịch thi
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-warning rounded-pill px-3">
                                            <i class="fas fa-exclamation-triangle me-1"></i>Chưa có lịch
                                        </span>
                                    }
                                </td>
                                <td>
                                    @if (exam.CreatedBy != null)
                                    {
                                        <div class="text-center">
                                            <div class="fw-bold">@exam.CreatedBy.FullName</div>
                                            <small class="text-muted">@exam.CreatedBy.EmployeeId</small>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="text-center">
                                            <div class="text-muted">
                                                <i class="fas fa-robot me-1"></i>Hệ thống
                                            </div>
                                        </div>
                                    }
                                </td>
                                <td class="text-center">
                                    <small class="text-muted">@exam.CreatedAt.ToString("dd/MM/yyyy")</small>
                                    <br><small class="text-muted">@exam.CreatedAt.ToString("HH:mm")</small>
                                </td>
                                <td class="text-center">
                                    @if (exam.IsActive)
                                    {
                                        <span class="badge bg-success rounded-pill px-3">
                                            <i class="fas fa-check-circle me-1"></i>Hoạt động
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary rounded-pill px-3">
                                            <i class="fas fa-pause-circle me-1"></i>Không hoạt động
                                        </span>
                                    }
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <div class="empty-state">
                    <div class="empty-state-icon mb-4">
                        <i class="fas fa-file-alt fa-4x" style="color: #a8e6cf;"></i>
                    </div>
                    <h4 class="text-muted mb-3">Chưa có bài kiểm tra nào</h4>
                    <p class="text-muted mb-4">Các giảng viên chưa tạo bài kiểm tra nào trong hệ thống</p>
                    <div class="text-muted">
                        <i class="fas fa-info-circle me-2"></i>
                        Bài kiểm tra sẽ được tạo bởi giảng viên
                    </div>
                </div>
            </div>
        }
    </div>
</div>

<!-- Enhanced Statistics Cards -->
<div class="container-fluid px-0 mt-4">
    <div class="row g-3 justify-content-center mx-auto" style="max-width: 1000px;">
        <!-- Total Exams Card -->
        <div class="col-lg-3 col-md-6">
            <div class="stat-card-modern stat-card-exams">
                <div class="stat-card-content">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon-bg stat-icon-bg-teal">
                            <i class="fas fa-file-alt"></i>
                        </div>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number">@Model.Count()</div>
                        <div class="stat-label">Tổng bài kiểm tra</div>
                        <div class="stat-description">Tất cả bài kiểm tra</div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <span class="stat-link">Chi tiết <i class="fas fa-arrow-right ms-1"></i></span>
                </div>
            </div>
        </div>

        <!-- Active Exams Card -->
        <div class="col-lg-3 col-md-6">
            <div class="stat-card-modern stat-card-students">
                <div class="stat-card-content">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon-bg stat-icon-bg-green">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number">@Model.Count(e => e.IsActive)</div>
                        <div class="stat-label">Đang hoạt động</div>
                        <div class="stat-description">Bài kiểm tra active</div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <span class="stat-link">Chi tiết <i class="fas fa-arrow-right ms-1"></i></span>
                </div>
            </div>
        </div>

        <!-- Exam Schedules Card -->
        <div class="col-lg-3 col-md-6">
            <div class="stat-card-modern stat-card-subjects">
                <div class="stat-card-content">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon-bg stat-icon-bg-orange">
                            <i class="fas fa-calendar"></i>
                        </div>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number">@Model.Sum(e => e.ExamSchedules.Count)</div>
                        <div class="stat-label">Lịch thi</div>
                        <div class="stat-description">Tổng số lịch thi</div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <span class="stat-link">Chi tiết <i class="fas fa-arrow-right ms-1"></i></span>
                </div>
            </div>
        </div>

        <!-- Inactive Exams Card -->
        <div class="col-lg-3 col-md-6">
            <div class="stat-card-modern stat-card-classes">
                <div class="stat-card-content">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon-bg stat-icon-bg-purple">
                            <i class="fas fa-pause-circle"></i>
                        </div>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number">@Model.Count(e => !e.IsActive)</div>
                        <div class="stat-label">Không hoạt động</div>
                        <div class="stat-description">Bài kiểm tra inactive</div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <span class="stat-link">Chi tiết <i class="fas fa-arrow-right ms-1"></i></span>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .table tbody tr {
        border: none;
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background-color: rgba(168, 230, 207, 0.1) !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .empty-state {
        animation: fadeInUp 0.6s ease-out;
    }

    .empty-state-icon {
        animation: float 3s ease-in-out infinite;
    }

    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @@keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-10px);
        }
    }

    .avatar-sm {
        transition: all 0.3s ease;
    }

    .avatar-sm:hover {
        transform: scale(1.05);
    }

    .badge {
        transition: all 0.3s ease;
    }

    .badge:hover {
        transform: scale(1.05);
    }
</style>

@section Scripts {
<script>
    // Enhanced Exams Page Interactions
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize components
        initializeFilters();
        initializeSearch();
        initializeExport();
        initializeAnimations();
    });

    function initializeFilters() {
        const filterBtn = document.getElementById('filterBtn');
        const filterPanel = document.getElementById('filterPanel');
        const applyFilter = document.getElementById('applyFilter');

        if (filterBtn && filterPanel) {
            filterBtn.addEventListener('click', function() {
                if (filterPanel.style.display === 'none' || !filterPanel.style.display) {
                    filterPanel.style.display = 'block';
                    filterPanel.classList.add('animate__animated', 'animate__fadeInDown');
                    this.innerHTML = '<i class="fas fa-times me-2"></i>Đóng';
                } else {
                    filterPanel.style.display = 'none';
                    this.innerHTML = '<i class="fas fa-filter me-2"></i>Lọc';
                }
            });
        }

        if (applyFilter) {
            applyFilter.addEventListener('click', function() {
                applyFilters();
            });
        }
    }

    function applyFilters() {
        const statusFilter = document.getElementById('statusFilter').value;
        const typeFilter = document.getElementById('typeFilter').value;
        const searchTerm = document.getElementById('examSearch').value.toLowerCase();

        const tableRows = document.querySelectorAll('tbody tr');

        tableRows.forEach(row => {
            let showRow = true;

            // Status filter
            if (statusFilter) {
                const statusBadge = row.querySelector('td:last-child .badge');
                const isActive = statusBadge && statusBadge.textContent.includes('Hoạt động');
                if ((statusFilter === 'active' && !isActive) ||
                    (statusFilter === 'inactive' && isActive)) {
                    showRow = false;
                }
            }

            // Search filter
            if (searchTerm) {
                const examTitle = row.cells[0].textContent.toLowerCase();
                const examDescription = row.cells[1].textContent.toLowerCase();
                if (!examTitle.includes(searchTerm) && !examDescription.includes(searchTerm)) {
                    showRow = false;
                }
            }

            // Show/hide row
            if (showRow) {
                row.style.display = '';
                row.style.animation = 'fadeIn 0.3s ease-out';
            } else {
                row.style.display = 'none';
            }
        });
    }

    function initializeSearch() {
        const searchInput = document.getElementById('examSearch');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                applyFilters();
            });
        }
    }

    function initializeExport() {
        const exportBtn = document.getElementById('exportBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', function() {
                // Simulate export functionality
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang xuất...';
                this.disabled = true;

                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-check me-2"></i>Đã xuất!';
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-download me-2"></i>Xuất';
                        this.disabled = false;
                    }, 2000);
                }, 1500);
            });
        }
    }

    function initializeAnimations() {
        // Animate table rows
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach((row, index) => {
            row.style.animationDelay = (index * 0.05) + 's';
        });

        // Animate stat cards
        const statCards = document.querySelectorAll('.stat-card-modern');
        statCards.forEach((card, index) => {
            card.style.animationDelay = (index * 0.1 + 0.5) + 's';
            card.classList.add('animate__animated', 'animate__fadeInUp');
        });
    }

    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @@keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .animate__fadeInDown {
            animation-duration: 0.5s;
        }
    `;
    document.head.appendChild(style);
</script>
}

