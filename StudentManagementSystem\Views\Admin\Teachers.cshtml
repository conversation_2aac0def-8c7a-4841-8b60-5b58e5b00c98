@model IEnumerable<StudentManagementSystem.Models.ApplicationUser>
@{
    ViewData["Title"] = "Quản lý giảng viên";
}

@section Styles {
    <link href="~/css/admin-ui.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet" />
}

<div class="admin-page-container">
<!-- Welcome Header with Animation -->
<div class="welcome-header mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <div class="position-relative z-index-2">
            <h2 class="mb-2 animate__animated animate__fadeInLeft fw-bold">
                <i class="fas fa-chalkboard-teacher me-2"></i>
                Quản lý giảng viên 👨‍🏫
            </h2>
            <p class="mb-0 opacity-75">Quản lý thông tin và theo dõi hoạt động của giảng viên</p>
        </div>
        <div class="text-end position-relative z-index-2">
            <a href="@Url.Action("CreateTeacher")" class="btn btn-light btn-lg px-4 py-2 rounded-pill shadow-sm">
                <i class="fas fa-plus me-2"></i> Thêm giảng viên mới
            </a>
        </div>
    </div>
</div>

@if (TempData["Success"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        @TempData["Success"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (TempData["Error"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        @TempData["Error"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<div class="admin-action-card animate__animated animate__fadeInUp">
    <div class="card-header">
        <h5 class="mb-0 fw-bold">
            <i class="fas fa-list me-2"></i> Danh sách giảng viên
        </h5>
        <!-- Search functionality -->
        <div class="mt-3">
            <div class="input-group">
                <span class="input-group-text bg-light border-0">
                    <i class="fas fa-search text-muted"></i>
                </span>
                <input type="text" id="teacherSearch" class="form-control border-0 bg-light"
                       placeholder="Tìm kiếm giảng viên theo tên, mã NV hoặc email...">
            </div>
        </div>
    </div>
    <div class="card-body">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-modern">
                    <thead>
                        <tr>
                            <th>Mã NV</th>
                            <th>Họ tên</th>
                            <th>Email</th>
                            <th>Lương</th>
                            <th>Ngày tạo</th>
                            <th class="text-center">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var teacher in Model)
                        {
                            <tr class="animate__animated animate__fadeIn" style="animation-delay: @(Model.ToList().IndexOf(teacher) * 0.1)s;">
                                <td class="fw-bold text-primary">@teacher.EmployeeId</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm me-2">
                                            <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                 style="width: 35px; height: 35px; background: linear-gradient(135deg, #a8e6cf, #7fcdcd);">
                                                <i class="fas fa-chalkboard-teacher text-white"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="fw-bold">@teacher.FullName</div>
                                            <small class="text-muted">Giảng viên</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="text-muted">@teacher.Email</span>
                                </td>
                                <td>
                                    @{
                                        var salaryColor = teacher.Salary >= 20000000 ? "success" :
                                                         teacher.Salary >= 15000000 ? "primary" :
                                                         teacher.Salary >= 10000000 ? "warning" : "secondary";
                                    }
                                    <span class="badge bg-@salaryColor rounded-pill px-3">
                                        @teacher.Salary.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">@teacher.CreatedAt.ToString("dd/MM/yyyy")</small>
                                </td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <a href="@Url.Action("EditTeacher", new { id = teacher.Id })"
                                           class="btn btn-sm btn-outline-primary rounded-pill me-1"
                                           data-bs-toggle="tooltip"
                                           title="Chỉnh sửa thông tin giảng viên">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-sm btn-outline-info rounded-pill me-1"
                                                data-bs-toggle="modal"
                                                data-bs-target="#<EMAIL>"
                                                title="Xem chi tiết giảng viên">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button"
                                                class="btn btn-sm btn-outline-danger rounded-pill"
                                                data-bs-toggle="modal"
                                                data-bs-target="#<EMAIL>"
                                                title="Xóa giảng viên khỏi hệ thống">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <div class="empty-state">
                    <div class="empty-state-icon mb-4">
                        <i class="fas fa-chalkboard-teacher fa-4x" style="color: #a8e6cf;"></i>
                    </div>
                    <h4 class="text-muted mb-3">Chưa có giảng viên nào</h4>
                    <p class="text-muted mb-4">Hãy thêm giảng viên đầu tiên cho hệ thống</p>
                    <a href="@Url.Action("CreateTeacher")" class="btn btn-success-gradient btn-lg rounded-pill px-4">
                        <i class="fas fa-plus me-2"></i> Thêm giảng viên mới
                    </a>
                </div>
            </div>
        }
    </div>
</div>

<!-- Detail Modals -->
@foreach (var teacher in Model)
{
    <div class="modal fade" id="<EMAIL>" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content border-0 shadow-lg">
                <div class="modal-header" style="background: linear-gradient(135deg, #a8e6cf, #7fcdcd); color: #2e7d32; border: none;">
                    <h5 class="modal-title fw-bold">
                        <i class="fas fa-chalkboard-teacher me-2"></i>Chi tiết giảng viên
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row">
                        <div class="col-md-4 text-center mb-4">
                            <div class="avatar-lg mx-auto mb-3">
                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                     style="width: 100px; height: 100px; background: linear-gradient(135deg, #a8e6cf, #7fcdcd);">
                                    <i class="fas fa-chalkboard-teacher fa-3x text-white"></i>
                                </div>
                            </div>
                            <h5 class="fw-bold text-primary">@teacher.FullName</h5>
                            <p class="text-muted">@teacher.EmployeeId</p>
                            @{
                                var salaryColor = teacher.Salary >= 20000000 ? "success" :
                                                 teacher.Salary >= 15000000 ? "primary" :
                                                 teacher.Salary >= 10000000 ? "warning" : "secondary";
                            }
                            <span class="badge bg-@salaryColor rounded-pill px-3 py-2">
                                @teacher.Salary.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))
                            </span>
                        </div>
                        <div class="col-md-8">
                            <div class="row g-3">
                                <div class="col-sm-6">
                                    <div class="info-card p-3 rounded" style="background: #f8f9fa;">
                                        <small class="text-muted d-block">Email</small>
                                        <strong>@teacher.Email</strong>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="info-card p-3 rounded" style="background: #f8f9fa;">
                                        <small class="text-muted d-block">Số điện thoại</small>
                                        <strong>@(teacher.PhoneNumber ?? "Chưa cập nhật")</strong>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="info-card p-3 rounded" style="background: #f8f9fa;">
                                        <small class="text-muted d-block">Địa chỉ</small>
                                        <strong>@(string.IsNullOrEmpty(teacher.Address) ? "Chưa cập nhật" : teacher.Address)</strong>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="info-card p-3 rounded" style="background: #f8f9fa;">
                                        <small class="text-muted d-block">Ngày sinh</small>
                                        <strong>@(teacher.DateOfBirth == default ? "Chưa cập nhật" : teacher.DateOfBirth.ToString("dd/MM/yyyy"))</strong>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="info-card p-3 rounded" style="background: #f8f9fa;">
                                        <small class="text-muted d-block">Ngày tạo</small>
                                        <strong>@teacher.CreatedAt.ToString("dd/MM/yyyy HH:mm")</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 p-4">
                    <button type="button" class="btn btn-light rounded-pill px-4" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Đóng
                    </button>
                    <a href="@Url.Action("EditTeacher", new { id = teacher.Id })" class="btn btn-primary-gradient rounded-pill px-4">
                        <i class="fas fa-edit me-2"></i>Chỉnh sửa
                    </a>
                </div>
            </div>
        </div>
    </div>
}

<!-- Delete Confirmation Modals -->
@foreach (var teacher in Model)
{
    <div class="modal fade" id="<EMAIL>" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content border-0 shadow-lg">
                <div class="modal-header bg-danger text-white border-0">
                    <h5 class="modal-title fw-bold">
                        <i class="fas fa-exclamation-triangle me-2"></i>Xác nhận xóa giảng viên
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="alert alert-warning border-0 rounded-3 mb-4" style="background: #fff3cd;">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle fa-2x text-warning me-3"></i>
                            <div>
                                <strong>Cảnh báo:</strong> Hành động này không thể hoàn tác!
                            </div>
                        </div>
                    </div>

                    <div class="text-center mb-4">
                        <div class="avatar-lg mx-auto mb-3">
                            <div class="rounded-circle d-flex align-items-center justify-content-center"
                                 style="width: 80px; height: 80px; background: linear-gradient(135deg, #dc3545, #c82333);">
                                <i class="fas fa-chalkboard-teacher fa-2x text-white"></i>
                            </div>
                        </div>
                        <h6 class="mb-3">Bạn có chắc chắn muốn xóa giảng viên sau không?</h6>
                    </div>

                    <div class="teacher-info p-3 rounded-3" style="background: #f8f9fa;">
                        <div class="row g-3">
                            <div class="col-sm-6">
                                <small class="text-muted d-block">Mã nhân viên</small>
                                <strong class="text-primary">@teacher.EmployeeId</strong>
                            </div>
                            <div class="col-sm-6">
                                <small class="text-muted d-block">Họ tên</small>
                                <strong>@teacher.FullName</strong>
                            </div>
                            <div class="col-12">
                                <small class="text-muted d-block">Email</small>
                                <strong>@teacher.Email</strong>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info border-0 rounded-3 mt-4" style="background: #d1ecf1;">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-info-circle fa-lg text-info me-3 mt-1"></i>
                            <div>
                                <strong>Lưu ý:</strong> Việc xóa giảng viên có thể ảnh hưởng đến các môn học và lớp học mà họ đang phụ trách.
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 p-4">
                    <button type="button" class="btn btn-light rounded-pill px-4 me-2" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Hủy
                    </button>
                    <form asp-action="DeleteTeacher" method="post" class="d-inline">
                        <input type="hidden" name="id" value="@teacher.Id" />
                        @Html.AntiForgeryToken()
                        <button type="submit" class="btn btn-danger-gradient rounded-pill px-4">
                            <i class="fas fa-trash me-2"></i>Xóa giảng viên
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
}

<style>
    .table tbody tr {
        border: none;
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background-color: rgba(168, 230, 207, 0.1) !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .btn-group .btn {
        margin-right: 3px;
        transition: all 0.3s ease;
    }

    .btn-group .btn:last-child {
        margin-right: 0;
    }

    .btn-group .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .info-card {
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
    }

    .info-card:hover {
        background-color: #e8f5e8 !important;
        border-color: #a8e6cf;
    }

    .teacher-info {
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .empty-state {
        animation: fadeInUp 0.6s ease-out;
    }

    .empty-state-icon {
        animation: float 3s ease-in-out infinite;
    }

    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @@keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-10px);
        }
    }

    .avatar-sm, .avatar-lg {
        transition: all 0.3s ease;
    }

    .avatar-sm:hover, .avatar-lg:hover {
        transform: scale(1.05);
    }
</style>

@section Scripts {
<script>
    // Enhanced Teachers Page Interactions
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        initializeTooltips();

        // Add table row animations
        addTableAnimations();

        // Handle modal events
        handleModalEvents();

        // Add search functionality
        addSearchFunctionality();
    });

    function initializeTooltips() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    function addTableAnimations() {
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach((row, index) => {
            row.style.animationDelay = (index * 0.05) + 's';

            // Add hover effect for action buttons
            const actionButtons = row.querySelectorAll('.btn');
            actionButtons.forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px) scale(1.05)';
                });

                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    }

    function handleModalEvents() {
        // Detail modals
        const detailModals = document.querySelectorAll('[id^="detailModal-"]');
        detailModals.forEach(modal => {
            modal.addEventListener('show.bs.modal', function() {
                this.querySelector('.modal-content').style.animation = 'slideInDown 0.3s ease-out';
            });
        });

        // Delete modals
        const deleteModals = document.querySelectorAll('[id^="deleteModal-"]');
        deleteModals.forEach(modal => {
            modal.addEventListener('show.bs.modal', function() {
                this.querySelector('.modal-content').style.animation = 'shake 0.5s ease-out';
            });
        });
    }

    function addSearchFunctionality() {
        const searchInput = document.getElementById('teacherSearch');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const tableRows = document.querySelectorAll('tbody tr');

                tableRows.forEach(row => {
                    const employeeId = row.cells[0].textContent.toLowerCase();
                    const teacherName = row.cells[1].textContent.toLowerCase();
                    const teacherEmail = row.cells[2].textContent.toLowerCase();

                    if (employeeId.includes(searchTerm) ||
                        teacherName.includes(searchTerm) ||
                        teacherEmail.includes(searchTerm)) {
                        row.style.display = '';
                        row.style.animation = 'fadeIn 0.3s ease-out';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        }
    }

    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @@keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @@keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        @@keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    `;
    document.head.appendChild(style);
</script>
}
</div> <!-- End admin-page-container -->
