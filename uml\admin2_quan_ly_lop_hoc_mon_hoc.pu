@startuml Admin_Class_Subject_Management
!theme plain
title Admin - Quản lý Lớp học và Môn học Thông minh

actor Admin
participant "AdminController" as AC
participant "ApplicationDbContext" as DB
participant "UserManager" as UM

== Tạo Lớp học mới ==
Admin -> AC: GET /Admin/CreateClass
activate AC

AC -> DB: Subjects.Where(s => s.IsActive).ToListAsync()
activate DB
DB --> AC: List<Subject>
deactivate DB

AC -> UM: GetUsersInRoleAsync("Teacher")
activate UM
UM --> AC: List<ApplicationUser> teachers
deactivate UM

AC --> Admin: CreateClassViewModel with Subjects & Teachers
deactivate AC

Admin -> AC: POST /Admin/CreateClass(CreateClassViewModel)
activate AC

AC -> AC: ValidateModel()
alt Model Valid
    AC -> DB: Classes.Add(newClass)
    activate DB
    
    loop for each selected subject
        AC -> DB: ClassSubjects.Add(new ClassSubject)
    end
    
    AC -> DB: SaveChangesAsync()
    DB --> AC: Success
    deactivate DB
    
    AC --> Admin: Redirect to Classes List + Success Message
else Model Invalid
    AC -> DB: Get Subjects & Teachers again
    activate DB
    DB --> AC: Data for dropdown
    deactivate DB
    AC --> Admin: Return View with Validation Errors
end

deactivate AC

== Phân công Sinh viên vào Lớp ==
Admin -> AC: GET /Admin/AssignStudents/{classId}
activate AC

AC -> DB: Classes.Include(ClassStudents).FirstOrDefaultAsync(id)
activate DB
DB --> AC: Class with current students
deactivate DB

AC -> UM: GetUsersInRoleAsync("Student")
activate UM
UM --> AC: All Students
deactivate UM

AC --> Admin: AssignStudentsViewModel
deactivate AC

Admin -> AC: POST /Admin/AssignStudents
activate AC

AC -> DB: Remove existing ClassStudents
activate DB

loop for each selected student
    AC -> DB: ClassStudents.Add(new ClassStudent)
end

AC -> DB: SaveChangesAsync()
DB --> AC: Success
deactivate DB

AC --> Admin: Redirect with Success Message
deactivate AC

@enduml