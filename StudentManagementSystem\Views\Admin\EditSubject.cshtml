@model StudentManagementSystem.ViewModels.EditSubjectViewModel
@{
    ViewData["Title"] = "Sửa môn học";
}

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4><i class="fas fa-edit"></i> Sửa môn học</h4>
            </div>
            <div class="card-body">
                <form asp-action="EditSubject" method="post">
                    <input asp-for="Id" type="hidden" />
                    <div asp-validation-summary="All" class="text-danger mb-3"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="SubjectCode" class="form-label"></label>
                                <input asp-for="SubjectCode" class="form-control" placeholder="VD: CS101" />
                                <span asp-validation-for="SubjectCode" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Credits" class="form-label"></label>
                                <input asp-for="Credits" class="form-control" type="number" min="1" max="10" />
                                <span asp-validation-for="Credits" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="SubjectName" class="form-label"></label>
                        <input asp-for="SubjectName" class="form-control" placeholder="Nhập tên môn học" />
                        <span asp-validation-for="SubjectName" class="text-danger"></span>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="TeacherUserId" class="form-label"></label>
                        <select asp-for="TeacherUserId" class="form-select">
                            <option value="">-- Chọn giảng viên phụ trách --</option>
                            @foreach (var teacher in ViewBag.Teachers as IEnumerable<StudentManagementSystem.Models.ApplicationUser>)
                            {
                                <option value="@teacher.Id">@teacher.FullName (@teacher.EmployeeId)</option>
                            }
                        </select>
                        <span asp-validation-for="TeacherUserId" class="text-danger"></span>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="Description" class="form-label"></label>
                        <textarea asp-for="Description" class="form-control" rows="4" placeholder="Nhập mô tả môn học (tùy chọn)"></textarea>
                        <span asp-validation-for="Description" class="text-danger"></span>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                            <label asp-for="IsActive" class="form-check-label">
                                Môn học đang hoạt động
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="@Url.Action("Subjects")" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Cập nhật môn học
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
