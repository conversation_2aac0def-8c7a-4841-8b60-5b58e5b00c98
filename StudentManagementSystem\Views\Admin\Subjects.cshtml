@model IEnumerable<StudentManagementSystem.Models.Subject>
@{
    ViewData["Title"] = "Quản lý môn học";
}

@section Styles {
    <link href="~/css/admin-ui.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet" />
}

<div class="admin-page-container">
    <!-- Welcome Header -->
    <div class="welcome-header mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div class="position-relative z-index-2">
                <h2 class="mb-2 animate__animated animate__fadeInLeft fw-bold">
                    <i class="fas fa-book me-2"></i>
                    Quản lý môn học 📚
                </h2>
                <p class="mb-0 opacity-75">Quản lý thông tin môn học và phân công giảng viên</p>
            </div>
            <div class="text-end position-relative z-index-2">
                <a href="@Url.Action("CreateSubject")" class="btn btn-light btn-lg px-4 py-2 rounded-pill shadow-sm">
                    <i class="fas fa-plus me-2"></i> Thêm môn học mới
                </a>
            </div>
        </div>
    </div>

@if (TempData["Success"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        @TempData["Success"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (TempData["Error"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        @TempData["Error"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<div class="admin-action-card animate__animated animate__fadeInUp">
    <div class="card-header">
        <h5 class="mb-0 fw-bold">
            <i class="fas fa-list me-2"></i> Danh sách môn học
        </h5>
    </div>
    <div class="card-body">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-modern">
                    <thead>
                        <tr>
                            <th>Mã môn học</th>
                            <th>Tên môn học</th>
                            <th>Số tín chỉ</th>
                            <th>Giảng viên phụ trách</th>
                            <th>Trạng thái</th>
                            <th class="text-center">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var subject in Model)
                        {
                            <tr>
                                <td><strong>@subject.SubjectCode</strong></td>
                                <td>@subject.SubjectName</td>
                                <td>@subject.Credits</td>
                                <td>@subject.Teacher.FullName</td>
                                <td>
                                    @if (subject.IsActive)
                                    {
                                        <span class="badge bg-success">Hoạt động</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">Ngừng hoạt động</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="@Url.Action("EditSubject", new { id = subject.Id })" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i> Sửa
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#<EMAIL>">
                                            <i class="fas fa-eye"></i> Xem
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#<EMAIL>">
                                            <i class="fas fa-trash"></i> Xóa
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="fas fa-book fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Chưa có môn học nào</h5>
                <p class="text-muted">Hãy thêm môn học đầu tiên cho hệ thống</p>
                <a href="@Url.Action("CreateSubject")" class="btn btn-success">
                    <i class="fas fa-plus"></i> Thêm môn học mới
                </a>
            </div>
        }
    </div>
</div>

<!-- Detail Modals -->
@foreach (var subject in Model)
{
    <div class="modal fade" id="<EMAIL>" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Chi tiết môn học</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <dl class="row">
                        <dt class="col-sm-4">Mã môn học:</dt>
                        <dd class="col-sm-8"><strong>@subject.SubjectCode</strong></dd>
                        
                        <dt class="col-sm-4">Tên môn học:</dt>
                        <dd class="col-sm-8">@subject.SubjectName</dd>
                        
                        <dt class="col-sm-4">Mô tả:</dt>
                        <dd class="col-sm-8">@(string.IsNullOrEmpty(subject.Description) ? "Chưa có mô tả" : subject.Description)</dd>
                        
                        <dt class="col-sm-4">Số tín chỉ:</dt>
                        <dd class="col-sm-8">@subject.Credits</dd>
                        
                        <dt class="col-sm-4">Giảng viên:</dt>
                        <dd class="col-sm-8">@subject.Teacher.FullName</dd>
                        
                        <dt class="col-sm-4">Trạng thái:</dt>
                        <dd class="col-sm-8">
                            @if (subject.IsActive)
                            {
                                <span class="badge bg-success">Hoạt động</span>
                            }
                            else
                            {
                                <span class="badge bg-secondary">Ngừng hoạt động</span>
                            }
                        </dd>
                        
                        <dt class="col-sm-4">Ngày tạo:</dt>
                        <dd class="col-sm-8">@subject.CreatedAt.ToString("dd/MM/yyyy HH:mm")</dd>
                    </dl>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                    <a href="@Url.Action("EditSubject", new { id = subject.Id })" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Chỉnh sửa
                    </a>
                </div>
            </div>
        </div>
    </div>
}

<!-- Delete Modals -->
@foreach (var subject in Model)
{
    <div class="modal fade" id="<EMAIL>" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">Xác nhận xóa</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Bạn có chắc chắn muốn xóa môn học <strong>@subject.SubjectCode - @subject.SubjectName</strong>?</p>
                    <p class="text-danger"><small><i class="fas fa-exclamation-triangle"></i> Hành động này không thể hoàn tác!</small></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <form asp-action="DeleteSubject" method="post" class="d-inline">
                        <input type="hidden" name="id" value="@subject.Id" />
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Xóa
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
}
</div> <!-- End admin-page-container -->
