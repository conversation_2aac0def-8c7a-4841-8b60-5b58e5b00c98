/* Admin UI Enhancements */

/* Welcome Header Animation */
.welcome-header {
    background: linear-gradient(135deg, #a8e6cf 0%, #7fcdcd 50%, #81c784 100%);
    border-radius: 20px;
    padding: 25px;
    color: #2e7d32;
    margin-bottom: 30px;
    box-shadow: 0 15px 35px rgba(129, 199, 132, 0.3);
    position: relative;
    overflow: hidden;
}

.welcome-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }

    50% {
        transform: translateY(-10px) rotate(5deg);
    }
}

.welcome-header h2 {
    font-weight: 600;
    margin-bottom: 5px;
}

/* Enhanced Statistics Cards - Modern Design */
.stat-card-modern {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    border: none;
    position: relative;
    height: 180px;
    display: flex;
    flex-direction: column;
}

.stat-card-modern:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.stat-card-content {
    padding: 25px 20px 15px;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-icon-wrapper {
    margin-bottom: 15px;
}

.stat-icon-bg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.stat-icon-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3));
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card-modern:hover .stat-icon-bg::before {
    opacity: 1;
}

.stat-card-modern:hover .stat-icon-bg {
    transform: scale(1.1) rotate(10deg);
}

/* Icon Background Colors */
.stat-icon-bg-blue {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon-bg-green {
    background: linear-gradient(135deg, #a8e6cf 0%, #7fcdcd 100%);
}

.stat-icon-bg-orange {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.stat-icon-bg-purple {
    background: linear-gradient(135deg, #c471ed 0%, #f64f59 100%);
}

.stat-icon-bg-teal {
    background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
}

.stat-icon-bg-red {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.stat-info {
    flex: 1;
}

.stat-number {
    font-size: 32px;
    font-weight: 800;
    color: #2c3e50;
    margin-bottom: 5px;
    transition: all 0.3s ease;
    line-height: 1;
}

.stat-card-modern:hover .stat-number {
    transform: scale(1.05);
    color: #1a252f;
}

.stat-label {
    font-size: 14px;
    font-weight: 600;
    color: #34495e;
    margin-bottom: 3px;
    text-transform: capitalize;
}

.stat-description {
    font-size: 11px;
    color: #7f8c8d;
    font-weight: 400;
}

.stat-card-footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 12px 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.stat-card-modern:hover .stat-card-footer {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.stat-link {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
    transition: all 0.3s ease;
}

.stat-card-modern:hover .stat-link {
    color: #495057;
    transform: translateX(3px);
}

/* Card Specific Hover Effects */
.stat-card-students:hover {
    box-shadow: 0 20px 50px rgba(102, 126, 234, 0.2);
}

.stat-card-teachers:hover {
    box-shadow: 0 20px 50px rgba(168, 230, 207, 0.2);
}

.stat-card-subjects:hover {
    box-shadow: 0 20px 50px rgba(252, 182, 159, 0.2);
}

.stat-card-classes:hover {
    box-shadow: 0 20px 50px rgba(196, 113, 237, 0.2);
}

.stat-card-exams:hover {
    box-shadow: 0 20px 50px rgba(102, 166, 255, 0.2);
}

.stat-card-submissions:hover {
    box-shadow: 0 20px 50px rgba(255, 154, 158, 0.2);
}

/* Color variants */
.stat-card-primary .stat-card-header {
    background: #007bff;
}

.stat-card-primary .stat-icon {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.stat-card-success .stat-card-header {
    background: #28a745;
}

.stat-card-success .stat-icon {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

.stat-card-warning .stat-card-header {
    background: #ffc107;
}

.stat-card-warning .stat-icon {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: white;
}

.stat-card-danger .stat-card-header {
    background: #dc3545;
}

.stat-card-danger .stat-icon {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.stat-card-info .stat-card-header {
    background: #17a2b8;
}

.stat-card-info .stat-icon {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.stat-card-secondary .stat-card-header {
    background: #6c757d;
}

.stat-card-secondary .stat-icon {
    background: linear-gradient(135deg, #6c757d, #545b62);
    color: white;
}

/* Admin Action Cards */
.admin-action-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: none;
    overflow: hidden;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.admin-action-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.admin-action-card .card-header {
    background: linear-gradient(135deg, #a8e6cf, #7fcdcd);
    color: #2e7d32;
    border: none;
    padding: 20px;
    font-weight: 600;
}

.admin-action-card .card-body {
    padding: 25px;
}

/* Admin Action Buttons */
.admin-action-btn {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-radius: 12px;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    font-weight: 500;
    position: relative;
    overflow: hidden;
    margin-bottom: 10px;
}

.admin-action-btn:last-child {
    margin-bottom: 0;
}

.admin-action-btn i {
    margin-right: 12px;
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.admin-action-btn:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Button Gradients */
.btn-primary-gradient {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.btn-primary-gradient:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    color: white;
}

.btn-success-gradient {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

.btn-success-gradient:hover {
    background: linear-gradient(135deg, #1e7e34, #155724);
    color: white;
}

.btn-warning-gradient {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

.btn-warning-gradient:hover {
    background: linear-gradient(135deg, #e0a800, #d39e00);
    color: #212529;
}

.btn-info-gradient {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.btn-info-gradient:hover {
    background: linear-gradient(135deg, #138496, #117a8b);
    color: white;
}

.btn-danger-gradient {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.btn-danger-gradient:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
    color: white;
}

.btn-secondary-gradient {
    background: linear-gradient(135deg, #6c757d, #545b62);
    color: white;
}

.btn-secondary-gradient:hover {
    background: linear-gradient(135deg, #545b62, #495057);
    color: white;
}

/* Quick Actions Bar */
.quick-actions {
    margin-bottom: 30px;
}

.quick-action-btn {
    padding: 15px 10px;
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 2px solid;
    background: white;
    height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.quick-action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.quick-action-btn:hover::before {
    left: 100%;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .stat-card-modern {
        height: 170px;
    }

    .stat-icon-bg {
        width: 55px;
        height: 55px;
        font-size: 22px;
    }

    .stat-number {
        font-size: 28px;
    }

    .admin-action-card .card-body {
        padding: 20px;
    }
}

@media (max-width: 992px) {
    .welcome-header {
        padding: 20px;
    }

    .welcome-header h2 {
        font-size: 1.5rem;
    }

    .stat-card-modern {
        height: 160px;
        margin-bottom: 15px;
    }

    .stat-icon-bg {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .stat-number {
        font-size: 26px;
    }

    .admin-action-btn {
        padding: 12px 18px;
        font-size: 14px;
    }

    .quick-action-btn {
        height: 70px;
        font-size: 12px;
    }
}

@media (max-width: 768px) {
    .welcome-header {
        padding: 15px;
        text-align: center;
        margin-bottom: 20px;
    }

    .welcome-header .d-flex {
        flex-direction: column;
        gap: 15px;
    }

    .welcome-header h2 {
        font-size: 1.3rem;
        margin-bottom: 8px;
    }

    .welcome-header p {
        font-size: 14px;
    }

    .stat-card-modern {
        height: 150px;
        margin-bottom: 10px;
    }

    .stat-card-content {
        padding: 20px 15px 10px;
    }

    .stat-icon-bg {
        width: 45px;
        height: 45px;
        font-size: 18px;
        margin-bottom: 10px;
    }

    .stat-number {
        font-size: 24px;
    }

    .stat-label {
        font-size: 13px;
    }

    .stat-description {
        font-size: 10px;
    }

    .stat-card-footer {
        padding: 10px 15px;
    }

    .admin-action-btn {
        padding: 10px 15px;
        font-size: 13px;
        margin-bottom: 8px;
    }

    .admin-action-btn i {
        margin-right: 8px;
        font-size: 14px;
    }

    .quick-action-btn {
        height: 65px;
        padding: 10px 8px;
        font-size: 11px;
    }

    .quick-action-btn i {
        font-size: 16px;
    }

    .admin-action-card .card-header {
        padding: 15px;
    }

    .admin-action-card .card-body {
        padding: 15px;
    }

    /* Table responsive improvements */
    .table-responsive {
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .table th,
    .table td {
        padding: 8px;
        font-size: 13px;
    }

    .btn-group .btn {
        padding: 4px 8px;
        font-size: 11px;
        margin-right: 2px;
    }

    .btn-group .btn i {
        font-size: 10px;
    }

    /* Modal improvements for mobile */
    .modal-dialog {
        margin: 10px;
    }

    .modal-content {
        border-radius: 15px;
    }

    .modal-header {
        padding: 15px;
    }

    .modal-body {
        padding: 15px;
    }

    .modal-footer {
        padding: 15px;
        flex-direction: column;
        gap: 10px;
    }

    .modal-footer .btn {
        width: 100%;
        margin: 0;
    }
}

@media (max-width: 576px) {
    .welcome-header {
        padding: 12px;
        border-radius: 15px;
    }

    .welcome-header h2 {
        font-size: 1.2rem;
    }

    .welcome-header p {
        font-size: 13px;
        display: none;
        /* Hide description on very small screens */
    }

    .stat-card-modern {
        height: 130px;
    }

    .stat-card-content {
        padding: 15px 10px 8px;
    }

    .stat-icon-bg {
        width: 40px;
        height: 40px;
        font-size: 16px;
        margin-bottom: 8px;
    }

    .stat-number {
        font-size: 22px;
    }

    .stat-label {
        font-size: 12px;
    }

    .stat-description {
        font-size: 9px;
        line-height: 1.2;
    }

    .stat-card-footer {
        padding: 8px 10px;
    }

    .stat-link {
        font-size: 10px;
    }

    .admin-action-btn {
        padding: 8px 12px;
        font-size: 12px;
        border-radius: 8px;
    }

    .quick-action-btn {
        height: 60px;
        padding: 8px 6px;
        font-size: 10px;
        border-radius: 8px;
    }

    .quick-action-btn i {
        font-size: 14px;
        margin-bottom: 2px;
    }

    /* Hide some table columns on very small screens */
    .table th:nth-child(4),
    .table td:nth-child(4),
    .table th:nth-child(5),
    .table td:nth-child(5) {
        display: none;
    }

    /* Stack action buttons vertically */
    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        width: 100%;
        margin-right: 0;
        margin-bottom: 2px;
        border-radius: 6px;
    }

    /* Improve modal for very small screens */
    .modal-dialog {
        margin: 5px;
        max-width: calc(100vw - 10px);
    }

    .modal-header h5 {
        font-size: 16px;
    }

    .modal-body {
        padding: 12px;
    }

    .modal-body .row .col-md-4,
    .modal-body .row .col-md-8 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .avatar-lg {
        width: 80px !important;
        height: 80px !important;
    }

    .avatar-lg i {
        font-size: 2rem !important;
    }

    .info-card {
        margin-bottom: 10px;
        padding: 10px !important;
    }

    .student-info {
        padding: 10px !important;
    }
}

/* Print styles */
@media print {

    .welcome-header,
    .quick-actions,
    .admin-action-card .card-header,
    .btn,
    .modal {
        display: none !important;
    }

    .admin-action-card {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .table {
        font-size: 12px;
    }

    .table th,
    .table td {
        padding: 4px;
        border: 1px solid #ddd;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .welcome-header {
        background: #000;
        color: #fff;
    }

    .stat-card-simple {
        border: 2px solid #000;
    }

    .admin-action-card {
        border: 2px solid #000;
    }

    .btn {
        border: 2px solid #000;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {

    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .welcome-header::before {
        animation: none;
    }

    .stat-card-simple:hover {
        transform: none;
    }

    .admin-action-btn:hover {
        transform: none;
    }
}

/* Animation Classes */
.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
    animation: slideUp 0.6s ease-out;
}

.animate-bounce-in {
    animation: bounceIn 0.8s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }

    50% {
        opacity: 1;
        transform: scale(1.05);
    }

    70% {
        transform: scale(0.9);
    }

    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(168, 230, 207, 0.7);
    }

    70% {
        box-shadow: 0 0 0 10px rgba(168, 230, 207, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(168, 230, 207, 0);
    }
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }

    100% {
        left: 100%;
    }
}

/* Interactive Elements */
.interactive-card {
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    cursor: pointer;
}

.interactive-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.interactive-card:active {
    transform: translateY(-2px) scale(1.01);
}

/* Notification Styles */
.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    animation: pulse 2s infinite;
}

/* Progress Indicators */
.progress-ring {
    width: 60px;
    height: 60px;
    transform: rotate(-90deg);
}

.progress-ring-circle {
    fill: none;
    stroke: #a8e6cf;
    stroke-width: 4;
    stroke-linecap: round;
    stroke-dasharray: 157;
    stroke-dashoffset: 157;
    transition: stroke-dashoffset 0.5s ease-in-out;
}

/* Glassmorphism Effect */
.glass-card {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Gradient Text */
.gradient-text {
    background: linear-gradient(135deg, #a8e6cf, #7fcdcd, #81c784);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}