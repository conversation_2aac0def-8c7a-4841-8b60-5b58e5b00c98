@model IEnumerable<StudentManagementSystem.Models.ExamSchedule>
@{
    ViewData["Title"] = "Bài thi có sẵn";
}

@section Styles {
    <link href="~/css/student-ui.css" rel="stylesheet" />
}

<div class="student-page-container">
    <!-- Welcome Header -->
    <div class="welcome-header mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div class="position-relative z-index-2">
                <h2 class="mb-2 fw-bold">
                    <i class="fas fa-file-alt me-2"></i>
                    Bài thi có sẵn 📝
                </h2>
                <p class="mb-0 opacity-75">Xem và tham gia các bài kiểm tra đang diễn ra</p>
            </div>
            <div class="text-end position-relative z-index-2">
                <a href="@Url.Action("Index")" class="btn btn-light btn-lg px-4 py-2 rounded-pill shadow-sm">
                    <i class="fas fa-arrow-left me-2"></i> Quay lại Dashboard
                </a>
            </div>
        </div>
    </div>

    <div class="admin-action-card">
        <div class="card-header">
            <h5 class="mb-0 fw-bold">
                <i class="fas fa-list me-2"></i> Danh sách bài thi
            </h5>
        </div>
        <div class="card-body">
            @if (Model != null && Model.Any())
            {
                <div class="row g-4">
                    @foreach (var examSchedule in Model)
                    {
                        var now = DateTime.Now;
                        var isAvailable = now >= examSchedule.StartTime && now <= examSchedule.EndTime && examSchedule.IsActive;
                        var hasEnded = now > examSchedule.EndTime;
                        var hasSubmitted = examSchedule.Submissions?.Any() ?? false;

                        <div class="col-md-6 col-lg-4">
                            <div class="exam-card">
                                <div class="exam-card-header">
                                    <div class="exam-status">
                                        @if (hasSubmitted)
                                        {
                                            <i class="fas fa-check-circle text-success"></i>
                                            <span>Đã nộp</span>
                                        }
                                        else if (isAvailable)
                                        {
                                            <i class="fas fa-play-circle text-primary"></i>
                                            <span>Có thể thi</span>
                                        }
                                        else if (hasEnded)
                                        {
                                            <i class="fas fa-times-circle text-danger"></i>
                                            <span>Đã kết thúc</span>
                                        }
                                        else
                                        {
                                            <i class="fas fa-clock text-warning"></i>
                                            <span>Sắp diễn ra</span>
                                        }
                                    </div>
                                    <h5 class="exam-title">@examSchedule.Exam.Title</h5>
                                    <p class="exam-class">Lớp: @(examSchedule.Class?.ClassName ?? examSchedule.ClassName)</p>
                                </div>

                                <div class="exam-card-body">
                                    @if (!string.IsNullOrEmpty(examSchedule.Exam.Description))
                                    {
                                        <div class="exam-info-item">
                                            <span class="exam-info-label">Mô tả:</span>
                                            <span class="exam-info-value">@examSchedule.Exam.Description</span>
                                        </div>
                                    }

                                    <div class="exam-info">
                                        <div class="exam-info-item">
                                            <span class="exam-info-label">Ngày thi:</span>
                                            <span class="exam-info-value">@examSchedule.StartTime.ToString("dd/MM/yyyy")</span>
                                        </div>
                                        <div class="exam-info-item">
                                            <span class="exam-info-label">Thời gian:</span>
                                            <span class="exam-info-value">@examSchedule.StartTime.ToString("HH:mm") - @examSchedule.EndTime.ToString("HH:mm")</span>
                                        </div>
                                        <div class="exam-info-item">
                                            <span class="exam-info-label">Thời lượng:</span>
                                            <span class="exam-info-value">@((examSchedule.EndTime - examSchedule.StartTime).TotalMinutes) phút</span>
                                        </div>
                                        @if (!string.IsNullOrEmpty(examSchedule.Location))
                                        {
                                            <div class="exam-info-item">
                                                <span class="exam-info-label">Địa điểm:</span>
                                                <span class="exam-info-value">@examSchedule.Location</span>
                                            </div>
                                        }
                                    </div>

                                    <div class="exam-actions mt-3">
                                        @if (isAvailable && !hasSubmitted)
                                        {
                                            <a href="@Url.Action("TakeExam", new { id = examSchedule.Id })" class="btn btn-primary btn-sm w-100">
                                                <i class="fas fa-play"></i> Bắt đầu thi
                                            </a>
                                        }
                                        else if (hasSubmitted)
                                        {
                                            <button class="btn btn-success btn-sm w-100" disabled>
                                                <i class="fas fa-check"></i> Đã nộp bài
                                            </button>
                                        }
                                        else if (hasEnded)
                                        {
                                            <button class="btn btn-secondary btn-sm w-100" disabled>
                                                <i class="fas fa-times"></i> Đã hết hạn
                                            </button>
                                        }
                                        else
                                        {
                                            <button class="btn btn-warning btn-sm w-100" disabled>
                                                <i class="fas fa-clock"></i> Chưa đến giờ
                                            </button>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Hiện tại không có bài thi nào</h5>
                    <p class="text-muted">Hãy kiểm tra lại sau hoặc liên hệ với giảng viên để biết thêm thông tin.</p>
                    <a href="@Url.Action("Index")" class="btn btn-primary">
                        <i class="fas fa-home"></i> Về Dashboard
                    </a>
                </div>
            }
        </div>
    </div>
</div>
