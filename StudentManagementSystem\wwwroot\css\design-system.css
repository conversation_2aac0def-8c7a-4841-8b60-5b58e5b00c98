/* ===================================
   STUDENT MANAGEMENT SYSTEM - DESIGN SYSTEM
   Modern, Consistent, Accessible UI Framework
   =================================== */

/* ===================================
   CSS CUSTOM PROPERTIES (VARIABLES)
   =================================== */

:root {
    /* === COLOR PALETTE === */
    /* Primary Green Theme */
    --color-primary: #a8e6cf;
    --color-primary-light: #c8f2e0;
    --color-primary-dark: #7fcdcd;
    --color-secondary: #81c784;
    --color-accent: #7fcdcd;

    /* Semantic Colors */
    --color-success: #28a745;
    --color-success-light: #d4edda;
    --color-warning: #ffc107;
    --color-warning-light: #fff3cd;
    --color-danger: #dc3545;
    --color-danger-light: #f8d7da;
    --color-info: #17a2b8;
    --color-info-light: #d1ecf1;

    /* Neutral Colors */
    --color-white: #ffffff;
    --color-gray-50: #f8f9fa;
    --color-gray-100: #e9ecef;
    --color-gray-200: #dee2e6;
    --color-gray-300: #ced4da;
    --color-gray-400: #adb5bd;
    --color-gray-500: #6c757d;
    --color-gray-600: #495057;
    --color-gray-700: #343a40;
    --color-gray-800: #212529;
    --color-gray-900: #000000;

    /* Text Colors */
    --color-text-primary: #2e7d32;
    --color-text-secondary: #495057;
    --color-text-muted: #6c757d;
    --color-text-light: #ffffff;

    /* Background Colors */
    --color-bg-primary: #ffffff;
    --color-bg-secondary: #f8f9fa;
    --color-bg-tertiary: #e9ecef;

    /* === TYPOGRAPHY === */
    --font-family-primary: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-family-heading: 'Segoe UI', system-ui, sans-serif;
    --font-family-mono: 'Consolas', 'Monaco', 'Courier New', monospace;

    /* Font Sizes */
    --font-size-xs: 0.75rem;
    /* 12px */
    --font-size-sm: 0.875rem;
    /* 14px */
    --font-size-base: 1rem;
    /* 16px */
    --font-size-lg: 1.125rem;
    /* 18px */
    --font-size-xl: 1.25rem;
    /* 20px */
    --font-size-2xl: 1.5rem;
    /* 24px */
    --font-size-3xl: 1.875rem;
    /* 30px */
    --font-size-4xl: 2.25rem;
    /* 36px */
    --font-size-5xl: 3rem;
    /* 48px */

    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;

    /* Line Heights */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* === SPACING === */
    --spacing-0: 0;
    --spacing-1: 0.25rem;
    /* 4px */
    --spacing-2: 0.5rem;
    /* 8px */
    --spacing-3: 0.75rem;
    /* 12px */
    --spacing-4: 1rem;
    /* 16px */
    --spacing-5: 1.25rem;
    /* 20px */
    --spacing-6: 1.5rem;
    /* 24px */
    --spacing-8: 2rem;
    /* 32px */
    --spacing-10: 2.5rem;
    /* 40px */
    --spacing-12: 3rem;
    /* 48px */
    --spacing-16: 4rem;
    /* 64px */
    --spacing-20: 5rem;
    /* 80px */
    --spacing-24: 6rem;
    /* 96px */

    /* === BORDERS === */
    --border-width-thin: 1px;
    --border-width-medium: 2px;
    --border-width-thick: 4px;

    --border-radius-sm: 0.375rem;
    /* 6px */
    --border-radius-base: 0.5rem;
    /* 8px */
    --border-radius-lg: 0.75rem;
    /* 12px */
    --border-radius-xl: 1rem;
    /* 16px */
    --border-radius-2xl: 1.5rem;
    /* 24px */
    --border-radius-full: 9999px;

    /* === SHADOWS === */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* === TRANSITIONS === */
    --transition-fast: 150ms ease-in-out;
    --transition-base: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;

    /* === Z-INDEX === */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;

    /* === BREAKPOINTS === */
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
    --breakpoint-xxl: 1400px;
}

/* ===================================
   GLOBAL STYLES
   =================================== */

* {
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--color-text-secondary);
    background-color: var(--color-bg-secondary);
    margin: 0;
    padding: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===================================
   TYPOGRAPHY UTILITIES
   =================================== */

.text-primary {
    color: var(--color-text-primary) !important;
}

.text-secondary {
    color: var(--color-text-secondary) !important;
}

.text-muted {
    color: var(--color-text-muted) !important;
}

.text-light {
    color: var(--color-text-light) !important;
}

.font-light {
    font-weight: var(--font-weight-light) !important;
}

.font-normal {
    font-weight: var(--font-weight-normal) !important;
}

.font-medium {
    font-weight: var(--font-weight-medium) !important;
}

.font-semibold {
    font-weight: var(--font-weight-semibold) !important;
}

.font-bold {
    font-weight: var(--font-weight-bold) !important;
}

.text-xs {
    font-size: var(--font-size-xs) !important;
}

.text-sm {
    font-size: var(--font-size-sm) !important;
}

.text-base {
    font-size: var(--font-size-base) !important;
}

.text-lg {
    font-size: var(--font-size-lg) !important;
}

.text-xl {
    font-size: var(--font-size-xl) !important;
}

.text-2xl {
    font-size: var(--font-size-2xl) !important;
}

.text-3xl {
    font-size: var(--font-size-3xl) !important;
}

/* ===================================
   SPACING UTILITIES
   =================================== */

.m-0 {
    margin: var(--spacing-0) !important;
}

.m-1 {
    margin: var(--spacing-1) !important;
}

.m-2 {
    margin: var(--spacing-2) !important;
}

.m-3 {
    margin: var(--spacing-3) !important;
}

.m-4 {
    margin: var(--spacing-4) !important;
}

.m-5 {
    margin: var(--spacing-5) !important;
}

.m-6 {
    margin: var(--spacing-6) !important;
}

.m-8 {
    margin: var(--spacing-8) !important;
}

.p-0 {
    padding: var(--spacing-0) !important;
}

.p-1 {
    padding: var(--spacing-1) !important;
}

.p-2 {
    padding: var(--spacing-2) !important;
}

.p-3 {
    padding: var(--spacing-3) !important;
}

.p-4 {
    padding: var(--spacing-4) !important;
}

.p-5 {
    padding: var(--spacing-5) !important;
}

.p-6 {
    padding: var(--spacing-6) !important;
}

.p-8 {
    padding: var(--spacing-8) !important;
}

/* ===================================
   BORDER UTILITIES
   =================================== */

.rounded-sm {
    border-radius: var(--border-radius-sm) !important;
}

.rounded {
    border-radius: var(--border-radius-base) !important;
}

.rounded-lg {
    border-radius: var(--border-radius-lg) !important;
}

.rounded-xl {
    border-radius: var(--border-radius-xl) !important;
}

.rounded-2xl {
    border-radius: var(--border-radius-2xl) !important;
}

.rounded-full {
    border-radius: var(--border-radius-full) !important;
}

/* ===================================
   SHADOW UTILITIES
   =================================== */

.shadow-sm {
    box-shadow: var(--shadow-sm) !important;
}

.shadow {
    box-shadow: var(--shadow-base) !important;
}

.shadow-md {
    box-shadow: var(--shadow-md) !important;
}

.shadow-lg {
    box-shadow: var(--shadow-lg) !important;
}

.shadow-xl {
    box-shadow: var(--shadow-xl) !important;
}

.shadow-2xl {
    box-shadow: var(--shadow-2xl) !important;
}

/* ===================================
   ANIMATION UTILITIES
   =================================== */

.transition-fast {
    transition: all var(--transition-fast) !important;
}

.transition {
    transition: all var(--transition-base) !important;
}

.transition-slow {
    transition: all var(--transition-slow) !important;
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
    animation: slideUp 0.5s ease-out;
}

.animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }

    50% {
        opacity: 1;
        transform: scale(1.05);
    }

    70% {
        transform: scale(0.9);
    }

    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* ===================================
   MODERN NAVIGATION STYLES
   =================================== */

.main-header {
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--color-gray-200);
}

.navbar-modern {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 50%, var(--color-secondary) 100%);
    padding: var(--spacing-3) 0;
    min-height: 70px;
}

.brand-modern {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--color-text-primary);
    font-weight: var(--font-weight-bold);
    transition: var(--transition-base);
}

.brand-modern:hover {
    color: var(--color-text-primary);
    transform: scale(1.02);
}

.brand-icon {
    width: 45px;
    height: 45px;
    background: var(--color-white);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-3);
    box-shadow: var(--shadow-md);
    color: var(--color-primary);
    font-size: var(--font-size-xl);
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    line-height: 1;
    color: var(--color-white);
}

.brand-subtitle {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.8);
    line-height: 1;
}

/* Custom Navbar Toggler */
.custom-toggler {
    border: none;
    padding: var(--spacing-2);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-base);
    transition: var(--transition-base);
}

.custom-toggler:hover {
    background: rgba(255, 255, 255, 0.2);
}

.toggler-icon {
    display: block;
    width: 22px;
    height: 2px;
    background: var(--color-white);
    margin: 4px 0;
    transition: var(--transition-base);
    border-radius: 1px;
}

/* Navigation Links */
.main-nav {
    gap: var(--spacing-2);
}

.nav-dropdown-modern .nav-link-modern {
    display: flex;
    align-items: center;
    padding: var(--spacing-3) var(--spacing-4);
    color: var(--color-white);
    text-decoration: none;
    border-radius: var(--border-radius-base);
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
}

.nav-link-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.nav-link-modern:hover::before {
    left: 100%;
}

.nav-link-modern:hover {
    color: var(--color-white);
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.nav-link-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.nav-icon {
    font-size: var(--font-size-lg);
    width: 20px;
    text-align: center;
}

.nav-text {
    font-weight: var(--font-weight-medium);
}

/* Dropdown Menus */
.dropdown-menu-modern {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-2);
    margin-top: var(--spacing-2);
    background: var(--color-white);
    min-width: 280px;
}

.dropdown-header {
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
}

.dropdown-item-modern {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3) var(--spacing-4);
    color: var(--color-text-secondary);
    text-decoration: none;
    border-radius: var(--border-radius-base);
    transition: var(--transition-base);
    margin: var(--spacing-1) 0;
}

.dropdown-item-modern:hover {
    background: var(--color-primary-light);
    color: var(--color-text-primary);
    transform: translateX(4px);
}

.dropdown-item-modern i {
    width: 18px;
    text-align: center;
    color: var(--color-primary);
}

/* User Navigation */
.user-nav {
    margin-left: auto;
}

.user-dropdown .user-nav-link {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-2) var(--spacing-3);
}

.user-avatar {
    width: 35px;
    height: 35px;
    background: var(--color-white);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-primary);
    margin-right: var(--spacing-2);
}

.user-info {
    display: flex;
    flex-direction: column;
    text-align: left;
}

.user-name {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--color-white);
    line-height: 1.2;
}

.user-role {
    font-size: var(--font-size-xs);
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.2;
}

.user-dropdown-menu {
    min-width: 250px;
}

.user-dropdown-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-4);
    background: var(--color-primary-light);
    border-radius: var(--border-radius-base);
    margin-bottom: var(--spacing-2);
}

.user-avatar-large {
    width: 45px;
    height: 45px;
    background: var(--color-primary);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-white);
    font-size: var(--font-size-lg);
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-details strong {
    color: var(--color-text-primary);
    font-size: var(--font-size-base);
}

.user-details small {
    color: var(--color-text-muted);
    font-size: var(--font-size-xs);
}

.logout-btn {
    color: var(--color-danger) !important;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.logout-btn:hover {
    background: var(--color-danger-light) !important;
    color: var(--color-danger) !important;
}

/* ===================================
   MAIN CONTENT LAYOUT
   =================================== */

.main-content {
    min-height: calc(100vh - 70px - 300px);
    /* Adjust based on header and footer height */
    padding: var(--spacing-6) 0 var(--spacing-12);
    background: var(--color-bg-secondary);
}

.main-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

.content-wrapper {
    background: var(--color-white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-8);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-6);
}

/* ===================================
   ADMIN PAGES
   =================================== */

.admin-page-container {
    margin-bottom: var(--spacing-16);
    position: relative;
    z-index: 1;
    overflow-x: hidden;
    width: 100%;
    max-width: 100%;
}

/* ===================================
   TEACHER PAGES
   =================================== */

.teacher-page-container {
    margin-bottom: var(--spacing-16);
    position: relative;
    z-index: 1;
    overflow-x: hidden;
    width: 100%;
    max-width: 100%;
}

/* ===================================
   STUDENT PAGES
   =================================== */

.student-page-container {
    margin-bottom: var(--spacing-16);
    position: relative;
    z-index: 1;
    overflow-x: hidden;
    width: 100%;
    max-width: 100%;
}

/* Exam Cards */
.exam-card {
    background: var(--color-white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-base);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.exam-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.exam-card-header {
    padding: var(--spacing-6);
    color: var(--color-white);
    text-align: center;
    position: relative;
}

.exam-card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: var(--transition-base);
}

.exam-card:hover .exam-card-header::before {
    opacity: 1;
}

.exam-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-3);
}

.exam-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-2);
    line-height: 1.3;
}

.exam-class {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    margin: 0;
}

.exam-card-body {
    padding: var(--spacing-6);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.exam-info {
    margin-bottom: var(--spacing-4);
}

.exam-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-2) 0;
    border-bottom: 1px solid var(--color-gray-100);
}

.exam-info-item:last-child {
    border-bottom: none;
}

.exam-info-label {
    font-weight: var(--font-weight-medium);
    color: var(--color-gray-600);
    font-size: var(--font-size-sm);
}

.exam-info-value {
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-900);
}

.exam-card-footer {
    padding: var(--spacing-4) var(--spacing-6);
    background: var(--color-gray-50);
    border-top: 1px solid var(--color-gray-100);
    margin-top: auto;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    transition: var(--transition-base);
    border: none;
    cursor: pointer;
    width: 100%;
}

.action-btn:hover {
    transform: translateY(-1px);
    text-decoration: none;
}

.take-exam-btn {
    background: linear-gradient(135deg, var(--color-primary), var(--color-blue-600));
    color: var(--color-white);
}

.take-exam-btn:hover {
    background: linear-gradient(135deg, var(--color-blue-600), var(--color-blue-700));
    color: var(--color-white);
}

.view-btn {
    background: linear-gradient(135deg, var(--color-success), var(--color-green-600));
    color: var(--color-white);
}

.view-btn:hover {
    background: linear-gradient(135deg, var(--color-green-600), var(--color-green-700));
    color: var(--color-white);
}

.status-display {
    display: flex;
    align-items: center;
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-3);
}

.status-display.available {
    background: rgba(0, 123, 255, 0.1);
    color: var(--color-primary);
}

.status-display.submitted {
    background: rgba(40, 167, 69, 0.1);
    color: var(--color-success);
}

.status-display.ended {
    background: rgba(108, 117, 125, 0.1);
    color: var(--color-gray-600);
}

.status-display.upcoming {
    background: rgba(255, 193, 7, 0.1);
    color: var(--color-warning);
}

/* Modern Table Styles */
.table-modern {
    border: none;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    background: var(--color-white);
}

.table-modern thead {
    background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-secondary-light) 100%);
}

.table-modern thead th {
    border: none;
    color: var(--color-primary-dark);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: var(--spacing-4) var(--spacing-3);
}

.table-modern tbody tr {
    border: none;
    transition: var(--transition-base);
}

.table-modern tbody tr:hover {
    background-color: var(--color-bg-secondary);
    transform: translateY(-1px);
}

.table-modern tbody td {
    border: none;
    padding: var(--spacing-4) var(--spacing-3);
    vertical-align: middle;
}

/* Modern Action Buttons */
.btn-group-modern {
    display: flex;
    gap: var(--spacing-2);
    justify-content: center;
}

.btn-action {
    width: 36px;
    height: 36px;
    border-radius: var(--border-radius-full);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    transition: var(--transition-base);
    text-decoration: none;
    cursor: pointer;
}

.btn-action-primary {
    background: var(--color-primary);
    color: var(--color-white);
}

.btn-action-primary:hover {
    background: var(--color-primary-dark);
    color: var(--color-white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-action-info {
    background: var(--color-info);
    color: var(--color-white);
}

.btn-action-info:hover {
    background: var(--color-info-dark);
    color: var(--color-white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-action-danger {
    background: var(--color-danger);
    color: var(--color-white);
}

.btn-action-danger:hover {
    background: var(--color-danger-dark);
    color: var(--color-white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Modern Forms */
.form-modern {
    padding: var(--spacing-6);
}

.form-group-modern {
    margin-bottom: var(--spacing-6);
}

.form-label-modern {
    display: block;
    margin-bottom: var(--spacing-2);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
    font-size: var(--font-size-sm);
}

.form-control-modern {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-4);
    border: 2px solid var(--color-border);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    transition: var(--transition-base);
    background: var(--color-white);
}

.form-control-modern:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(168, 230, 207, 0.1);
}

.form-control-modern:hover {
    border-color: var(--color-primary-light);
}

.form-error {
    display: block;
    margin-top: var(--spacing-1);
    font-size: var(--font-size-sm);
    color: var(--color-danger);
}

/* ===================================
   STUDENT DASHBOARD
   =================================== */

.student-dashboard-container {
    margin-bottom: var(--spacing-16);
    position: relative;
    z-index: 1;
    overflow-x: hidden;
    width: 100%;
    max-width: 100%;
}

/* ===================================
   DEADLINE ITEMS
   =================================== */

.deadline-item {
    position: relative;
    z-index: 1;
    border: 1px solid var(--color-gray-200);
    transition: var(--transition-base);
    overflow: hidden;
    word-wrap: break-word;
    max-width: 100%;
    box-sizing: border-box;
    margin-left: 0;
    margin-right: 0;
}

.deadline-item:hover {
    box-shadow: var(--shadow-sm);
    transform: translateY(-1px);
}

/* Fix for deadline cards overflow */
.deadline-card {
    height: auto !important;
    min-height: 300px;
    max-height: 500px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.deadline-card .card-body {
    overflow-y: auto;
    overflow-x: hidden;
    word-wrap: break-word;
    flex: 1;
    max-height: 400px;
}

.card-body {
    overflow: hidden;
    word-wrap: break-word;
}

/* Ensure Bootstrap grid doesn't overflow */
.row {
    margin-left: 0;
    margin-right: 0;
    max-width: 100%;
}

.col,
.col-md-6,
.col-lg-4,
.col-xl-4 {
    padding-left: 15px;
    padding-right: 15px;
    max-width: 100%;
    box-sizing: border-box;
}

/* ===================================
   MODERN FOOTER
   =================================== */

.modern-footer {
    background: linear-gradient(135deg, var(--color-gray-800) 0%, var(--color-gray-700) 100%);
    color: var(--color-white);
    padding: var(--spacing-12) 0 var(--spacing-6);
    margin-top: auto;
    position: relative;
    z-index: 10;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-8);
    margin-bottom: var(--spacing-8);
}

.footer-brand {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-4);
}

.footer-logo {
    width: 50px;
    height: 50px;
    background: var(--color-primary);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-white);
    font-size: var(--font-size-2xl);
    flex-shrink: 0;
}

.footer-info h6 {
    color: var(--color-white);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-2);
}

.footer-info p {
    color: var(--color-gray-300);
    font-size: var(--font-size-sm);
    margin: 0;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-6);
}

.footer-section h6 {
    color: var(--color-white);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-4);
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section li {
    margin-bottom: var(--spacing-2);
}

.footer-section a {
    color: var(--color-gray-300);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: var(--transition-base);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.footer-section a:hover {
    color: var(--color-primary);
    transform: translateX(4px);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-6);
    border-top: 1px solid var(--color-gray-600);
}

.copyright p {
    color: var(--color-gray-400);
    font-size: var(--font-size-sm);
    margin: 0;
}

.footer-social {
    display: flex;
    gap: var(--spacing-3);
}

.footer-social a {
    width: 40px;
    height: 40px;
    background: var(--color-gray-600);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-white);
    text-decoration: none;
    transition: var(--transition-base);
}

.footer-social a:hover {
    background: var(--color-primary);
    transform: translateY(-2px);
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

/* Mobile First Approach */
@media (max-width: 575.98px) {

    /* Extra small devices (phones) */
    .container-fluid {
        padding-left: var(--spacing-3);
        padding-right: var(--spacing-3);
    }

    .welcome-header h2 {
        font-size: var(--font-size-xl);
        line-height: 1.2;
    }

    .welcome-header p {
        font-size: var(--font-size-sm);
    }

    .welcome-header .d-flex {
        flex-direction: column;
        gap: var(--spacing-3);
        text-align: center;
    }

    .btn-group {
        flex-direction: column;
        gap: var(--spacing-2);
        width: 100%;
    }

    .btn-lg {
        padding: var(--spacing-3) var(--spacing-4);
        font-size: var(--font-size-base);
        width: 100%;
    }

    .stat-card-simple {
        margin-bottom: var(--spacing-4);
        min-height: 100px;
    }

    .stat-number {
        font-size: var(--font-size-xl);
    }

    .exam-card {
        margin-bottom: var(--spacing-4);
    }

    .table-responsive {
        font-size: var(--font-size-sm);
    }

    .modal-dialog {
        margin: var(--spacing-2);
        max-width: calc(100% - var(--spacing-4));
    }

    .navbar-nav .nav-link {
        padding: var(--spacing-3);
        text-align: center;
    }

    .dropdown-menu {
        position: static !important;
        transform: none !important;
        width: 100%;
        margin: 0;
        border: none;
        box-shadow: none;
    }
}

@media (min-width: 576px) and (max-width: 767.98px) {

    /* Small devices (landscape phones) */
    .welcome-header .d-flex {
        flex-direction: column;
        gap: var(--spacing-4);
        text-align: center;
    }

    .stat-card-simple {
        min-height: 120px;
    }

    .btn-group {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .brand-text {
        display: none;
    }

    .nav-text {
        display: none;
    }

    .user-info {
        display: none;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
    }

    .footer-bottom {
        flex-direction: column;
        gap: var(--spacing-4);
        text-align: center;
    }

    .content-wrapper {
        padding: var(--spacing-4);
        border-radius: var(--border-radius-lg);
    }

    .main-container {
        padding: 0 var(--spacing-2);
    }

    .student-dashboard-container,
    .admin-page-container,
    .teacher-page-container,
    .student-page-container {
        margin-bottom: var(--spacing-8);
        overflow-x: hidden;
        padding: 0 var(--spacing-2);
    }

    .deadline-card {
        min-height: 250px;
        max-height: 400px;
    }

    .deadline-card .card-body {
        max-height: 300px;
        padding: var(--spacing-3);
    }

    .deadline-item {
        margin-left: 0 !important;
        margin-right: 0 !important;
        padding-left: var(--spacing-2);
        padding-right: var(--spacing-2);
    }

    .btn-group-modern {
        flex-direction: column;
        gap: var(--spacing-2);
    }

    .btn-action {
        width: 100%;
        height: 40px;
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-1);
    }

    .table-modern {
        font-size: var(--font-size-sm);
    }

    .table-modern th,
    .table-modern td {
        padding: var(--spacing-2);
        vertical-align: middle;
    }

    .form-modern {
        padding: var(--spacing-3);
    }

    .form-control-modern {
        font-size: var(--font-size-base);
        padding: var(--spacing-3);
        min-height: 44px;
        /* Touch-friendly */
    }

    .card {
        margin-bottom: var(--spacing-4);
    }

    .navbar-nav {
        text-align: center;
    }

    /* Touch-friendly buttons */
    .btn {
        min-height: 44px;
        padding: var(--spacing-3) var(--spacing-4);
    }

    /* Improved spacing for mobile */
    .row {
        margin-left: calc(var(--spacing-2) * -1);
        margin-right: calc(var(--spacing-2) * -1);
    }

    .col,
    [class*="col-"] {
        padding-left: var(--spacing-2);
        padding-right: var(--spacing-2);
    }
}

@media (min-width: 768px) and (max-width: 991.98px) {

    /* Medium devices (tablets) */
    .welcome-header h2 {
        font-size: var(--font-size-2xl);
    }

    .stat-card-simple {
        min-height: 140px;
    }

    .exam-card {
        min-height: 300px;
    }

    .container-fluid {
        padding-left: var(--spacing-4);
        padding-right: var(--spacing-4);
    }
}

@media (min-width: 992px) and (max-width: 1199.98px) {

    /* Large devices (desktops) */
    .container-fluid {
        max-width: 960px;
        margin: 0 auto;
    }

    .welcome-header h2 {
        font-size: var(--font-size-3xl);
    }
}

@media (min-width: 1200px) {

    /* Extra large devices (large desktops) */
    .container-fluid {
        max-width: 1140px;
        margin: 0 auto;
    }

    .welcome-header h2 {
        font-size: var(--font-size-4xl);
    }

    .stat-card-simple {
        min-height: 160px;
    }
}

@media (max-width: 576px) {
    .footer-links {
        grid-template-columns: 1fr;
    }

    .dropdown-menu-modern {
        min-width: 250px;
        margin-left: -100px;
    }

    .user-dropdown-menu {
        min-width: 200px;
        margin-left: -150px;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {

    .btn:hover,
    .card:hover,
    .exam-card:hover {
        transform: none;
    }

    .btn {
        min-height: 48px;
        /* Larger touch targets */
    }

    .form-control {
        min-height: 48px;
    }

    .nav-link {
        min-height: 48px;
        display: flex;
        align-items: center;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {

    .stat-icon,
    .exam-card-header {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}