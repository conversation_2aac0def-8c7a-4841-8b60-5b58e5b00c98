/* ===================================
   MOBILE OPTIMIZATIONS
   =================================== */

/* Prevent horizontal scrolling */
html, body {
    overflow-x: hidden;
    width: 100%;
}

/* Touch-friendly interactions */
.touch-friendly {
    min-height: 44px;
    min-width: 44px;
    padding: 12px;
}

/* Mobile navigation improvements */
@media (max-width: 768px) {
    .navbar-toggler {
        border: none;
        padding: 8px;
        font-size: 1.2rem;
    }
    
    .navbar-collapse {
        background: var(--color-white);
        border-radius: var(--border-radius-lg);
        margin-top: var(--spacing-2);
        padding: var(--spacing-4);
        box-shadow: var(--shadow-lg);
    }
    
    .navbar-nav .nav-item {
        margin-bottom: var(--spacing-2);
    }
    
    .navbar-nav .nav-link {
        padding: var(--spacing-3) var(--spacing-4);
        border-radius: var(--border-radius-md);
        transition: var(--transition-base);
    }
    
    .navbar-nav .nav-link:hover {
        background: var(--color-primary-50);
        color: var(--color-primary);
    }
}

/* Mobile form improvements */
@media (max-width: 768px) {
    .form-control,
    .form-select,
    .form-control-modern {
        font-size: 16px; /* Prevent zoom on iOS */
        min-height: 44px;
        padding: 12px 16px;
    }
    
    .form-label {
        font-weight: var(--font-weight-semibold);
        margin-bottom: var(--spacing-2);
    }
    
    .form-group-modern {
        margin-bottom: var(--spacing-4);
    }
    
    .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        border-radius: var(--border-radius-md) !important;
        margin-bottom: var(--spacing-2);
    }
}

/* Mobile table improvements */
@media (max-width: 768px) {
    .table-responsive {
        border: none;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .table-modern {
        min-width: 600px;
        font-size: 14px;
    }
    
    .table-modern th,
    .table-modern td {
        padding: 8px 12px;
        white-space: nowrap;
    }
    
    .table-modern .btn-action {
        padding: 4px 8px;
        font-size: 12px;
        min-width: 32px;
        height: 32px;
    }
}

/* Mobile card improvements */
@media (max-width: 768px) {
    .card {
        border-radius: var(--border-radius-lg);
        margin-bottom: var(--spacing-4);
        box-shadow: var(--shadow-sm);
    }
    
    .card-header {
        padding: var(--spacing-4);
        border-bottom: 1px solid var(--color-gray-200);
    }
    
    .card-body {
        padding: var(--spacing-4);
    }
    
    .card-footer {
        padding: var(--spacing-4);
        border-top: 1px solid var(--color-gray-200);
    }
}

/* Mobile modal improvements */
@media (max-width: 768px) {
    .modal-dialog {
        margin: var(--spacing-3);
        max-width: calc(100% - var(--spacing-6));
    }
    
    .modal-content {
        border-radius: var(--border-radius-lg);
    }
    
    .modal-header {
        padding: var(--spacing-4);
        border-bottom: 1px solid var(--color-gray-200);
    }
    
    .modal-body {
        padding: var(--spacing-4);
        max-height: 60vh;
        overflow-y: auto;
    }
    
    .modal-footer {
        padding: var(--spacing-4);
        border-top: 1px solid var(--color-gray-200);
        flex-direction: column;
        gap: var(--spacing-2);
    }
    
    .modal-footer .btn {
        width: 100%;
    }
}

/* Mobile exam card improvements */
@media (max-width: 768px) {
    .exam-card {
        margin-bottom: var(--spacing-4);
    }
    
    .exam-card-header {
        padding: var(--spacing-4);
        text-align: center;
    }
    
    .exam-title {
        font-size: var(--font-size-lg);
        line-height: 1.3;
        margin-bottom: var(--spacing-2);
    }
    
    .exam-class {
        font-size: var(--font-size-sm);
        opacity: 0.9;
    }
    
    .exam-card-body {
        padding: var(--spacing-4);
    }
    
    .exam-info-item {
        padding: var(--spacing-2) 0;
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-1);
    }
    
    .exam-card-footer {
        padding: var(--spacing-4);
    }
    
    .action-btn {
        width: 100%;
        padding: var(--spacing-3) var(--spacing-4);
        font-size: var(--font-size-base);
    }
}

/* Mobile stat card improvements */
@media (max-width: 768px) {
    .stat-card-simple {
        min-height: 100px;
        margin-bottom: var(--spacing-3);
    }
    
    .stat-number {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-bold);
    }
    
    .stat-label {
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-medium);
    }
    
    .stat-icon {
        font-size: var(--font-size-lg);
    }
}

/* Mobile welcome header improvements */
@media (max-width: 768px) {
    .welcome-header {
        padding: var(--spacing-4) 0;
        margin-bottom: var(--spacing-4);
    }
    
    .welcome-header h2 {
        font-size: var(--font-size-xl);
        line-height: 1.2;
        margin-bottom: var(--spacing-2);
    }
    
    .welcome-header p {
        font-size: var(--font-size-sm);
        line-height: 1.4;
    }
    
    .welcome-header .d-flex {
        flex-direction: column;
        gap: var(--spacing-3);
        text-align: center;
    }
    
    .welcome-header .btn {
        width: 100%;
        margin-bottom: var(--spacing-2);
    }
}

/* Accessibility improvements for mobile */
@media (max-width: 768px) {
    /* Focus indicators */
    .btn:focus,
    .form-control:focus,
    .nav-link:focus {
        outline: 2px solid var(--color-primary);
        outline-offset: 2px;
    }
    
    /* Skip links for screen readers */
    .skip-link {
        position: absolute;
        top: -40px;
        left: 6px;
        background: var(--color-primary);
        color: var(--color-white);
        padding: 8px;
        text-decoration: none;
        border-radius: 4px;
        z-index: 1000;
    }
    
    .skip-link:focus {
        top: 6px;
    }
}

/* Performance optimizations for mobile */
@media (max-width: 768px) {
    /* Reduce animations on mobile for better performance */
    .animate__animated {
        animation-duration: 0.3s;
    }
    
    /* Optimize images */
    img {
        max-width: 100%;
        height: auto;
        image-rendering: optimizeQuality;
    }
    
    /* Optimize shadows */
    .shadow-sm {
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }
    
    .shadow {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .shadow-lg {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
}
