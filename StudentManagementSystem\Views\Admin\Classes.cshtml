@model IEnumerable<StudentManagementSystem.Models.Class>
@{
    ViewData["Title"] = "Quản lý lớp học";
}

@section Styles {
    <link href="~/css/admin-ui.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet" />
}

<div class="admin-page-container">
    <!-- Welcome Header -->
    <div class="welcome-header mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div class="position-relative z-index-2">
                <h2 class="mb-2 animate__animated animate__fadeInLeft fw-bold">
                    <i class="fas fa-school me-2"></i>
                    Quản lý lớp học 🏫
                </h2>
                <p class="mb-0 opacity-75">Quản lý thông tin lớp học và phân công giảng viên</p>
            </div>
            <div class="text-end position-relative z-index-2">
                <a href="@Url.Action("CreateClass")" class="btn btn-light btn-lg px-4 py-2 rounded-pill shadow-sm">
                    <i class="fas fa-plus me-2"></i> Thêm lớp học mới
                </a>
            </div>
        </div>
    </div>

@if (TempData["Success"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        @TempData["Success"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (TempData["Error"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        @TempData["Error"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<div class="admin-action-card animate__animated animate__fadeInUp">
    <div class="card-header">
        <h5 class="mb-0 fw-bold">
            <i class="fas fa-list me-2"></i> Danh sách lớp học
        </h5>
    </div>
    <div class="card-body">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-modern">
                    <thead>
                        <tr>
                            <th>Tên lớp</th>
                            <th>Môn học</th>
                            <th>Giảng viên</th>
                            <th>Học kỳ</th>
                            <th>Năm học</th>
                            <th>Sinh viên</th>
                            <th>Trạng thái</th>
                            <th class="text-center">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var classItem in Model)
                        {
                            <tr>
                                <td><strong>@classItem.ClassName</strong></td>
                                <td>
                                    @if (classItem.ClassSubjects?.Any() == true)
                                    {
                                        <div>
                                            @foreach (var cs in classItem.ClassSubjects)
                                            {
                                                @if (cs.Subject != null)
                                                {
                                                    <span class="badge bg-primary me-1">@cs.Subject.SubjectName (@cs.Subject.SubjectCode)</span>
                                                }
                                            }
                                        </div>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Chưa có môn học</span>
                                    }
                                </td>
                                <td>@(classItem.TeacherUser != null ? $"{classItem.TeacherUser.FirstName} {classItem.TeacherUser.LastName}" : "Chưa phân công")</td>
                                <td>@classItem.Semester</td>
                                <td>@classItem.Year</td>
                                <td>
                                    <span class="badge bg-info">
                                        @(classItem.ClassStudents?.Count(cs => cs.IsActive) ?? 0)/@classItem.MaxStudents
                                    </span>
                                </td>
                                <td>
                                    @if (classItem.IsActive)
                                    {
                                        <span class="badge bg-success">Hoạt động</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">Ngừng hoạt động</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="@Url.Action("EditClass", new { id = classItem.Id })" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i> Sửa
                                        </a>
                                        <a href="@Url.Action("AssignStudents", new { id = classItem.Id })" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-users"></i> Phân công
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#<EMAIL>">
                                            <i class="fas fa-trash"></i> Xóa
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-4">
                <i class="fas fa-school fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Chưa có lớp học nào</h5>
                <p class="text-muted">Hãy thêm lớp học đầu tiên của bạn!</p>
                <a href="@Url.Action("CreateClass")" class="btn btn-success">
                    <i class="fas fa-plus"></i> Thêm lớp học mới
                </a>
            </div>
        }
    </div>
</div>

<!-- Delete Modals -->
@foreach (var classItem in Model)
{
    <div class="modal fade" id="<EMAIL>" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Xác nhận xóa</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Bạn có chắc chắn muốn xóa lớp học <strong>@classItem.ClassName</strong>?</p>
                    <p class="text-danger"><small>Hành động này không thể hoàn tác!</small></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <form asp-action="DeleteClass" method="post" style="display: inline;">
                        <input type="hidden" name="id" value="@classItem.Id" />
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Xóa
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
}
</div> <!-- End admin-page-container -->
