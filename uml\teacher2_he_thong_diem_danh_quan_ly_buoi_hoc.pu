@startuml Teacher_Attendance_System
!theme plain
title Teacher - <PERSON><PERSON> thống Điểm danh và Quản lý Buổi học

actor Teacher
participant "TeacherController" as TC
participant "ApplicationDbContext" as DB
participant "ExcelService" as ES
participant "UserManager" as UM

== Tạo Buổi học ==
Teacher -> TC: GET /Teacher/CreateAttendanceSession
activate TC

TC -> UM: GetUserAsync(User)
activate UM
UM --> TC: Current Teacher
deactivate UM

TC -> DB: Classes.Where(TeacherUserId == currentUser.Id)
activate DB
DB --> TC: Teacher's Classes
deactivate DB

TC --> Teacher: CreateAttendanceSessionViewModel
deactivate TC

Teacher -> TC: POST /Teacher/CreateAttendanceSession
activate TC

TC -> DB: AttendanceSessions.Add(newSession)
activate DB
TC -> DB: SaveChangesAsync()
DB --> TC: Session Created
deactivate DB

TC --> Teacher: Redirect to Sessions List + Success Message
deactivate TC

== Điể<PERSON> danh <PERSON> viên ==
Teacher -> TC: GET /Teacher/TakeAttendance/{sessionId}
activate TC

TC -> DB: AttendanceSessions.Include(Class.ClassStudents.Student).FirstOrDefaultAsync(id)
activate DB
DB --> TC: Session with Students List
deactivate DB

TC -> DB: Attendances.Where(SessionId == sessionId).ToListAsync()
activate DB
DB --> TC: Existing Attendance Records
deactivate DB

TC --> Teacher: TakeAttendanceViewModel with Students
deactivate TC

Teacher -> TC: POST /Teacher/TakeAttendance
activate TC

loop for each student
    alt Attendance exists
        TC -> DB: Update existing Attendance record
        activate DB
        DB --> TC: Updated
        deactivate DB
    else New attendance
        TC -> DB: Attendances.Add(new Attendance)
        activate DB
        DB --> TC: Added
        deactivate DB
    end
end

TC -> DB: SaveChangesAsync()
activate DB
DB --> TC: All Attendance Saved
deactivate DB

TC --> Teacher: Attendance Recorded Successfully
deactivate TC

== Xuất Báo cáo Excel ==
Teacher -> TC: GET /Teacher/ExportAttendanceReport/{classId}?fromDate&toDate
activate TC

TC -> DB: Classes.Include(ClassStudents, AttendanceSessions.Attendances).FirstOrDefaultAsync(id)
activate DB
DB --> TC: Complete Class Data with Attendance
deactivate DB

TC -> ES: GenerateAttendanceReport(classData, fromDate, toDate)
activate ES

ES -> ES: CreateExcelWorkbook()
ES -> ES: AddStudentRows()
ES -> ES: AddAttendanceColumns()
ES -> ES: CalculateAttendancePercentages()

ES --> TC: Excel File (byte[])
deactivate ES

TC --> Teacher: Download Excel File
deactivate TC

@enduml