@model StudentManagementSystem.ViewModels.StudentScheduleOverviewViewModel
@{
    ViewData["Title"] = "Lịch học";
}

@section Styles {
    <link href="~/css/admin-ui.css" rel="stylesheet" />
    <link href="~/css/student-ui.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet" />
}

<div class="student-page-container">
<!-- Welcome Header with Animation -->
<div class="welcome-header mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <div class="position-relative z-index-2">
            <h2 class="mb-2 animate__animated animate__fadeInLeft fw-bold">
                <i class="fas fa-calendar-alt me-2"></i>
                Lịch học của tôi 📅
            </h2>
            <p class="mb-0 opacity-75"><PERSON>em lịch học và quản lý thời gian biểu</p>
        </div>
        <div class="text-end position-relative z-index-2">
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-outline-light rounded-pill px-3" data-bs-toggle="modal" data-bs-target="#calendarModal">
                    <i class="fas fa-calendar me-2"></i>Xem lịch
                </button>
                <a href="@Url.Action("ExportSchedule")" class="btn btn-outline-light rounded-pill px-3">
                    <i class="fas fa-file-excel me-2"></i>Xuất Excel
                </a>
                <a href="@Url.Action("Index")" class="btn btn-light btn-lg px-4 py-2 rounded-pill shadow-sm">
                    <i class="fas fa-arrow-left me-2"></i>Về Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Statistics Cards -->
<div class="container-fluid px-0 mb-4">
    <div class="row g-3 justify-content-center mx-auto" style="max-width: 1000px;">
        <!-- Total Schedules Card -->
        <div class="col-lg-3 col-md-6">
            <div class="stat-card-modern stat-card-exams">
                <div class="stat-card-content">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon-bg stat-icon-bg-teal">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number">@Model.TotalSchedules</div>
                        <div class="stat-label">Tổng buổi học</div>
                        <div class="stat-description">Buổi học đã lên lịch</div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <span class="stat-link">Chi tiết <i class="fas fa-arrow-right ms-1"></i></span>
                </div>
            </div>
        </div>

        <!-- Today's Classes Card -->
        <div class="col-lg-3 col-md-6">
            <div class="stat-card-modern stat-card-students">
                <div class="stat-card-content">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon-bg stat-icon-bg-orange">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number">@Model.TodayCount</div>
                        <div class="stat-label">Hôm nay</div>
                        <div class="stat-description">Buổi học hôm nay</div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <span class="stat-link">Chi tiết <i class="fas fa-arrow-right ms-1"></i></span>
                </div>
            </div>
        </div>

        <!-- Attended Classes Card -->
        <div class="col-lg-3 col-md-6">
            <div class="stat-card-modern stat-card-subjects">
                <div class="stat-card-content">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon-bg stat-icon-bg-green">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number">@Model.AttendedCount</div>
                        <div class="stat-label">Đã tham gia</div>
                        <div class="stat-description">Buổi đã điểm danh</div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <span class="stat-link">Chi tiết <i class="fas fa-arrow-right ms-1"></i></span>
                </div>
            </div>
        </div>

        <!-- Attendance Rate Card -->
        <div class="col-lg-3 col-md-6">
            <div class="stat-card-modern stat-card-classes">
                <div class="stat-card-content">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon-bg stat-icon-bg-purple">
                            <i class="fas fa-percentage"></i>
                        </div>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number">@Model.AttendanceRate.ToString("F1")%</div>
                        <div class="stat-label">Tỷ lệ tham gia</div>
                        <div class="stat-description">Điểm danh trung bình</div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <span class="stat-link">Chi tiết <i class="fas fa-arrow-right ms-1"></i></span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Student Info -->
<div class="admin-action-card animate__animated animate__fadeInUp mb-4">
    <div class="card-header">
        <h5 class="mb-0 fw-bold">
            <i class="fas fa-user-graduate me-2"></i> Thông tin sinh viên
        </h5>
    </div>
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-6">
                <div class="info-item">
                    <label class="info-label">Họ và tên</label>
                    <div class="info-value">@Model.StudentName</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="info-item">
                    <label class="info-label">Mã sinh viên</label>
                    <div class="info-value">@Model.StudentId</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Today's Classes -->
@if (Model.TodaySchedules.Any())
{
    <div class="admin-action-card animate__animated animate__fadeInUp mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #ffc107, #fd7e14);">
            <h5 class="mb-0 fw-bold text-white">
                <i class="fas fa-calendar-day me-2"></i> Lịch học hôm nay (@Model.TodaySchedules.Count)
            </h5>
        </div>
        <div class="card-body">
            <div class="row g-4">
                @foreach (var schedule in Model.TodaySchedules)
                {
                    <div class="col-md-6 col-lg-4">
                        <div class="schedule-card today-schedule animate__animated animate__fadeIn" style="animation-delay: @(Model.TodaySchedules.ToList().IndexOf(schedule) * 0.1)s;">
                            <div class="schedule-card-header">
                                <div class="schedule-icon">
                                    <i class="fas fa-chalkboard-teacher"></i>
                                </div>
                                <div class="schedule-info">
                                    <h6 class="schedule-title">@schedule.SessionTitle</h6>
                                    <div class="schedule-status">
                                        <span class="status-badge <EMAIL>">@schedule.Status</span>
                                    </div>
                                </div>
                            </div>

                            <div class="schedule-card-body">
                                <div class="subject-info mb-3">
                                    <h6 class="section-title">Môn học</h6>
                                    <div class="subject-display">
                                        <span class="subject-name">@schedule.SubjectName</span>
                                        <span class="class-name">@schedule.ClassName</span>
                                    </div>
                                </div>

                                <div class="teacher-info mb-3">
                                    <h6 class="section-title">Giảng viên</h6>
                                    <div class="teacher-display">
                                        <i class="fas fa-user me-1"></i>
                                        <span>@schedule.TeacherName</span>
                                    </div>
                                </div>

                                <div class="time-info mb-3">
                                    <h6 class="section-title">Thời gian</h6>
                                    <div class="time-display">
                                        <i class="fas fa-clock me-1"></i>
                                        <span>@schedule.StartTime.ToString(@"hh\:mm") - @schedule.EndTime.ToString(@"hh\:mm")</span>
                                    </div>
                                </div>

                                <div class="location-info mb-3">
                                    <h6 class="section-title">Địa điểm</h6>
                                    <div class="location-display">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <span>@schedule.Location</span>
                                    </div>
                                </div>

                                <div class="attendance-info">
                                    @if (schedule.HasAttended)
                                    {
                                        <div class="attendance-status attended">
                                            <i class="fas fa-check-circle me-1"></i>
                                            <span>@schedule.AttendanceStatus</span>
                                        </div>
                                    }
                                    else if (schedule.IsCompleted)
                                    {
                                        <div class="attendance-status missed">
                                            <i class="fas fa-times-circle me-1"></i>
                                            <span>Chưa điểm danh</span>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="attendance-status pending">
                                            <i class="fas fa-clock me-1"></i>
                                            <span>Chờ điểm danh</span>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
}

<!-- Upcoming Classes -->
@if (Model.UpcomingSchedules.Any())
{
    <div class="admin-action-card animate__animated animate__fadeInUp mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #007bff, #6610f2);">
            <h5 class="mb-0 fw-bold text-white">
                <i class="fas fa-calendar-plus me-2"></i> Lịch học sắp tới (@Model.UpcomingSchedules.Count)
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover schedule-table">
                    <thead>
                        <tr>
                            <th>Buổi học</th>
                            <th>Môn học</th>
                            <th>Lớp</th>
                            <th>Giảng viên</th>
                            <th>Thời gian</th>
                            <th>Địa điểm</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var schedule in Model.UpcomingSchedules.Take(10))
                        {
                            <tr class="schedule-row">
                                <td>
                                    <div class="session-info">
                                        <strong class="session-title">@schedule.SessionTitle</strong>
                                        @if (!string.IsNullOrEmpty(schedule.SessionDescription))
                                        {
                                            <br><small class="session-description">@schedule.SessionDescription</small>
                                        }
                                    </div>
                                </td>
                                <td>
                                    <span class="subject-badge">@schedule.SubjectName</span>
                                </td>
                                <td>
                                    <span class="class-badge">@schedule.ClassName</span>
                                </td>
                                <td>
                                    <div class="teacher-info">
                                        <i class="fas fa-user me-1"></i>
                                        <span>@schedule.TeacherName</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="time-info">
                                        <div class="date-display">@schedule.SessionDate.ToString("dd/MM/yyyy")</div>
                                        <div class="time-display">@schedule.StartTime.ToString(@"hh\:mm") - @schedule.EndTime.ToString(@"hh\:mm")</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="location-info">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <span>@schedule.Location</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="status-badge <EMAIL>">@schedule.Status</span>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-outline-info btn-sm rounded-pill"
                                            data-bs-toggle="modal"
                                            data-bs-target="#detailModal"
                                            data-schedule='@Html.Raw(System.Text.Json.JsonSerializer.Serialize(schedule))'>
                                        <i class="fas fa-info me-1"></i>Chi tiết
                                    </button>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
            @if (Model.UpcomingSchedules.Count > 10)
            {
                <div class="text-center mt-3">
                    <small class="text-muted">Hiển thị 10 buổi gần nhất. Tổng cộng: @Model.UpcomingSchedules.Count buổi</small>
                </div>
            }
        </div>
    </div>
}

<!-- Completed Classes -->
@if (Model.CompletedSchedules.Any())
{
    <div class="card">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0"><i class="fas fa-history"></i> Lịch sử buổi học (@Model.CompletedSchedules.Count)</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Buổi học</th>
                            <th>Môn học</th>
                            <th>Thời gian</th>
                            <th>Điểm danh</th>
                            <th>Trạng thái</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var schedule in Model.CompletedSchedules.Take(15))
                        {
                            <tr>
                                <td>
                                    <strong>@schedule.SessionTitle</strong><br>
                                    <small class="text-muted">@schedule.ClassName</small>
                                </td>
                                <td>@schedule.SubjectName</td>
                                <td>
                                    @schedule.SessionDate.ToString("dd/MM/yyyy")<br>
                                    <small class="text-muted">@schedule.StartTime.ToString(@"hh\:mm") - @schedule.EndTime.ToString(@"hh\:mm")</small>
                                </td>
                                <td>
                                    @if (schedule.HasAttended)
                                    {
                                        <span class="badge bg-success">@schedule.AttendanceStatus</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">Chưa điểm danh</span>
                                    }
                                </td>
                                <td>
                                    <span class="badge <EMAIL>">@schedule.Status</span>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
            @if (Model.CompletedSchedules.Count > 15)
            {
                <div class="text-center mt-3">
                    <small class="text-muted">Hiển thị 15 buổi gần nhất. Tổng cộng: @Model.CompletedSchedules.Count buổi</small>
                </div>
            }
        </div>
    </div>
}

@if (!Model.UpcomingSchedules.Any() && !Model.TodaySchedules.Any() && !Model.CompletedSchedules.Any())
{
    <div class="text-center py-5">
        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">Chưa có lịch học nào</h5>
        <p class="text-muted">Giảng viên chưa tạo lịch học cho các lớp bạn tham gia.</p>
    </div>
}

<!-- Detail Modal -->
<div class="modal fade" id="detailModal" tabindex="-1" aria-labelledby="detailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailModalLabel">
                    <i class="fas fa-info-circle"></i> Chi tiết buổi học
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="modalContent">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Handle detail modal
    document.addEventListener('DOMContentLoaded', function() {
        const detailModal = document.getElementById('detailModal');
        const modalContent = document.getElementById('modalContent');
        
        detailModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const scheduleData = JSON.parse(button.getAttribute('data-schedule'));

            const sessionDate = new Date(scheduleData.SessionDate);

            modalContent.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Thông tin buổi học:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item"><strong>Tiêu đề:</strong> ${scheduleData.SessionTitle}</li>
                            <li class="list-group-item"><strong>Môn học:</strong> ${scheduleData.SubjectName}</li>
                            <li class="list-group-item"><strong>Lớp:</strong> ${scheduleData.ClassName}</li>
                            <li class="list-group-item"><strong>Giảng viên:</strong> ${scheduleData.TeacherName}</li>
                            <li class="list-group-item"><strong>Điểm danh:</strong> ${scheduleData.AttendanceStatus}</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Thời gian và địa điểm:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item"><strong>Ngày học:</strong> ${sessionDate.toLocaleDateString('vi-VN')}</li>
                            <li class="list-group-item"><strong>Giờ bắt đầu:</strong> ${scheduleData.StartTime}</li>
                            <li class="list-group-item"><strong>Giờ kết thúc:</strong> ${scheduleData.EndTime}</li>
                            <li class="list-group-item"><strong>Địa điểm:</strong> ${scheduleData.Location}</li>
                            <li class="list-group-item"><strong>Trạng thái:</strong> <span class="badge bg-${scheduleData.StatusClass}">${scheduleData.Status}</span></li>
                        </ul>
                    </div>
                </div>
                ${scheduleData.SessionDescription ? `
                <div class="mt-3">
                    <h6>Mô tả buổi học:</h6>
                    <div class="alert alert-info">
                        ${scheduleData.SessionDescription}
                    </div>
                </div>
                ` : ''}
            `;
        });
    });
</script>

<style>
    .info-item {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 10px;
        border-left: 4px solid #a8e6cf;
    }

    .info-label {
        font-size: 12px;
        font-weight: bold;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 5px;
    }

    .info-value {
        font-size: 16px;
        font-weight: 500;
        color: #333;
    }

    .schedule-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .schedule-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    .today-schedule {
        border-left: 5px solid #ffc107;
    }

    .schedule-card-header {
        padding: 20px;
        background: linear-gradient(135deg, #a8e6cf, #7fcdcd);
        color: white;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .schedule-icon {
        width: 50px;
        height: 50px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
    }

    .schedule-title {
        margin: 0;
        font-size: 18px;
        font-weight: bold;
    }

    .schedule-card-body {
        padding: 20px;
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .section-title {
        font-size: 14px;
        font-weight: bold;
        color: #666;
        margin-bottom: 8px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .subject-display {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .subject-name {
        font-weight: 600;
        color: #333;
    }

    .class-name {
        font-size: 14px;
        color: #666;
        background: #f8f9fa;
        padding: 2px 8px;
        border-radius: 10px;
        display: inline-block;
        width: fit-content;
    }

    .teacher-display, .time-display, .location-display {
        color: #333;
        font-size: 14px;
    }

    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        display: inline-block;
    }

    .status-primary {
        background: #cce5ff;
        color: #004085;
    }

    .status-warning {
        background: #fff3cd;
        color: #856404;
    }

    .status-success {
        background: #d4edda;
        color: #155724;
    }

    .status-danger {
        background: #f8d7da;
        color: #721c24;
    }

    .attendance-status {
        padding: 10px;
        border-radius: 8px;
        text-align: center;
        font-weight: 500;
    }

    .attendance-status.attended {
        background: #d4edda;
        color: #155724;
    }

    .attendance-status.missed {
        background: #f8d7da;
        color: #721c24;
    }

    .attendance-status.pending {
        background: #fff3cd;
        color: #856404;
    }

    .schedule-table {
        border: none;
    }

    .schedule-table thead th {
        background: linear-gradient(135deg, #a8e6cf, #7fcdcd);
        color: white;
        border: none;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 12px;
        letter-spacing: 0.5px;
        padding: 15px 10px;
    }

    .schedule-row {
        border: none;
        transition: all 0.3s ease;
    }

    .schedule-row:hover {
        background-color: rgba(168, 230, 207, 0.1) !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .schedule-row td {
        border: none;
        padding: 15px 10px;
        vertical-align: middle;
    }

    .session-info .session-title {
        color: #333;
        font-weight: 600;
    }

    .session-info .session-description {
        color: #666;
        font-style: italic;
    }

    .subject-badge, .class-badge {
        background: linear-gradient(135deg, #a8e6cf, #7fcdcd);
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: 500;
        display: inline-block;
    }

    .time-info .date-display {
        font-weight: 600;
        color: #333;
        margin-bottom: 2px;
    }

    .time-info .time-display {
        font-size: 12px;
        color: #666;
    }

    .teacher-info, .location-info {
        color: #333;
        font-size: 14px;
    }

    @@media (max-width: 768px) {
        .schedule-card-header {
            padding: 15px;
        }

        .schedule-card-body {
            padding: 15px;
        }

        .schedule-icon {
            width: 40px;
            height: 40px;
            font-size: 16px;
        }

        .schedule-title {
            font-size: 16px;
        }

        .schedule-table {
            font-size: 14px;
        }

        .schedule-table thead th {
            padding: 10px 5px;
        }

        .schedule-row td {
            padding: 10px 5px;
        }
    }
</style>

@section Scripts {
<script>
    // Enhanced Student Schedule Page
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize animations
        initializeAnimations();

        // Initialize modal functionality
        initializeModalHandlers();

        // Initialize table interactions
        initializeTableInteractions();
    });

    function initializeAnimations() {
        // Animate schedule cards
        const scheduleCards = document.querySelectorAll('.schedule-card');
        scheduleCards.forEach((card, index) => {
            card.style.animationDelay = (index * 0.1) + 's';
        });

        // Animate stat cards
        const statCards = document.querySelectorAll('.stat-card-modern');
        statCards.forEach((card, index) => {
            card.style.animationDelay = (index * 0.1 + 0.5) + 's';
            card.classList.add('animate__animated', 'animate__fadeInUp');
        });

        // Animate table rows
        const tableRows = document.querySelectorAll('.schedule-row');
        tableRows.forEach((row, index) => {
            row.style.animationDelay = (index * 0.05) + 's';
            row.classList.add('animate__animated', 'animate__fadeInUp');
        });
    }

    function initializeModalHandlers() {
        // Enhanced modal handling is already in the existing script
        console.log('Modal handlers initialized');
    }

    function initializeTableInteractions() {
        // Add click handlers for table rows
        const tableRows = document.querySelectorAll('.schedule-row');
        tableRows.forEach(row => {
            row.addEventListener('click', function(e) {
                if (!e.target.closest('button')) {
                    // Highlight clicked row
                    tableRows.forEach(r => r.classList.remove('table-active'));
                    this.classList.add('table-active');
                }
            });
        });
    }
</script>
}
</div> <!-- End student-page-container -->
