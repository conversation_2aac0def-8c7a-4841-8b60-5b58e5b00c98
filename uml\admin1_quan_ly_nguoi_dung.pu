@startuml Admin_User_Management
!theme plain
title Admin - Qu<PERSON>n lý <PERSON>ời dùng <PERSON>àn diện

actor Admin
participant "AdminController" as AC
participant "UserManager" as UM
participant "RoleManager" as RM
participant "ApplicationDbContext" as DB
participant "Email Service" as ES

== Tạo Tài khoản Giảng viên ==
Admin -> AC: POST /Admin/CreateTeacher
activate AC

AC -> AC: ValidateModel(CreateUserViewModel)
alt Model Valid
    AC -> UM: CreateAsync(ApplicationUser, password)
    activate UM
    UM -> DB: INSERT INTO AspNetUsers
    activate DB
    DB --> UM: Success
    deactivate DB
    UM --> AC: IdentityResult.Succeeded
    deactivate UM
    
    AC -> UM: AddToRoleAsync(user, "Teacher")
    activate UM
    UM -> RM: AddToRoleAsync(user, role)
    activate RM
    RM -> DB: INSERT INTO AspNetUserRoles
    activate DB
    DB --> RM: Success
    deactivate DB
    RM --> UM: Success
    deactivate RM
    UM --> AC: Success
    deactivate UM
    
    AC -> ES: SendWelcomeEmail(user)
    activate ES
    ES --> AC: Email Sent
    deactivate ES
    
    AC --> Admin: Redirect to Teachers List + Success Message
else Model Invalid
    AC --> Admin: Return View with Validation Errors
end

deactivate AC

== Phân Quyền và Quản lý ==
Admin -> AC: GET /Admin/EditTeacher/{id}
activate AC
AC -> UM: FindByIdAsync(id)
activate UM
UM -> DB: SELECT FROM AspNetUsers WHERE Id = {id}
activate DB
DB --> UM: ApplicationUser
deactivate DB
UM --> AC: User Data
deactivate UM

AC -> UM: GetRolesAsync(user)
activate UM
UM -> DB: SELECT Roles FROM AspNetUserRoles
activate DB
DB --> UM: User Roles
deactivate DB
UM --> AC: List<string> roles
deactivate UM

AC --> Admin: EditUserViewModel with current data
deactivate AC

@enduml