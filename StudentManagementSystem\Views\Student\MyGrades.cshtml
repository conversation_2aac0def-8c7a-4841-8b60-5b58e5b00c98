@model StudentManagementSystem.ViewModels.StudentGradesOverviewViewModel
@{
    ViewData["Title"] = "<PERSON><PERSON><PERSON>m số của tôi";
}

@section Styles {
    <link href="~/css/admin-ui.css" rel="stylesheet" />
    <link href="~/css/student-ui.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet" />
}

<div class="student-page-container">
<!-- Welcome Header with Animation -->
<div class="welcome-header mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <div class="position-relative z-index-2">
            <h2 class="mb-2 animate__animated animate__fadeInLeft fw-bold">
                <i class="fas fa-star me-2"></i>
                <PERSON><PERSON><PERSON><PERSON> số của tôi ⭐
            </h2>
            <p class="mb-0 opacity-75"><PERSON>em tổng quan điểm số và thành tích học tập</p>
        </div>
        <div class="text-end position-relative z-index-2">
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-outline-light rounded-pill px-3" data-bs-toggle="modal" data-bs-target="#gradeSystemModal">
                    <i class="fas fa-info-circle me-2"></i>Hệ thống chấm điểm
                </button>
                <a href="@Url.Action("Index")" class="btn btn-light btn-lg px-4 py-2 rounded-pill shadow-sm">
                    <i class="fas fa-arrow-left me-2"></i>Quay lại Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Student Info and Overall GPA -->
<div class="admin-action-card animate__animated animate__fadeInUp mb-4">
    <div class="card-header">
        <h5 class="mb-0 fw-bold">
            <i class="fas fa-user-graduate me-2"></i> Thông tin sinh viên
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <div class="student-info">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">Họ và tên</label>
                                <div class="info-value">@Model.StudentName</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">Mã sinh viên</label>
                                <div class="info-value">@Model.StudentId</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">Học kỳ</label>
                                <div class="info-value">@Model.Semester</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">Năm học</label>
                                <div class="info-value">@Model.Year</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="gpa-display">
                    <div class="gpa-card">
                        <div class="gpa-icon">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <div class="gpa-info">
                            <div class="gpa-value">@Model.OverallGPA.ToString("F2")</div>
                            <div class="gpa-label">GPA Tổng kết</div>
                            <div class="gpa-scale">(Thang điểm 4.0)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Statistics Cards -->
<div class="container-fluid px-0 mb-4">
    <div class="row g-3 justify-content-center mx-auto" style="max-width: 1000px;">
        <!-- Total Subjects Card -->
        <div class="col-lg-3 col-md-6">
            <div class="stat-card-modern stat-card-exams">
                <div class="stat-card-content">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon-bg stat-icon-bg-teal">
                            <i class="fas fa-book"></i>
                        </div>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number">@Model.TotalSubjects</div>
                        <div class="stat-label">Tổng môn học</div>
                        <div class="stat-description">Môn đã học</div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <span class="stat-link">Chi tiết <i class="fas fa-arrow-right ms-1"></i></span>
                </div>
            </div>
        </div>

        <!-- Passed Subjects Card -->
        <div class="col-lg-3 col-md-6">
            <div class="stat-card-modern stat-card-students">
                <div class="stat-card-content">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon-bg stat-icon-bg-green">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number">@Model.Grades.Count(g => g.TotalScore >= 5.0m)</div>
                        <div class="stat-label">Môn đã qua</div>
                        <div class="stat-description">Điểm ≥ 5.0</div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <span class="stat-link">Chi tiết <i class="fas fa-arrow-right ms-1"></i></span>
                </div>
            </div>
        </div>

        <!-- Failed Subjects Card -->
        <div class="col-lg-3 col-md-6">
            <div class="stat-card-modern stat-card-subjects">
                <div class="stat-card-content">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon-bg stat-icon-bg-orange">
                            <i class="fas fa-times-circle"></i>
                        </div>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number">@Model.Grades.Count(g => g.TotalScore < 5.0m)</div>
                        <div class="stat-label">Môn chưa qua</div>
                        <div class="stat-description">Điểm < 5.0</div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <span class="stat-link">Chi tiết <i class="fas fa-arrow-right ms-1"></i></span>
                </div>
            </div>
        </div>

        <!-- Average Score Card -->
        <div class="col-lg-3 col-md-6">
            <div class="stat-card-modern stat-card-classes">
                <div class="stat-card-content">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon-bg stat-icon-bg-purple">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number">@(Model.Grades.Any() ? Model.Grades.Average(g => g.TotalScore).ToString("F1") : "0.0")</div>
                        <div class="stat-label">Điểm trung bình</div>
                        <div class="stat-description">Thang điểm 10</div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <span class="stat-link">Chi tiết <i class="fas fa-arrow-right ms-1"></i></span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Grades by Subject -->
<div class="admin-action-card animate__animated animate__fadeInUp">
    <div class="card-header">
        <h5 class="mb-0 fw-bold">
            <i class="fas fa-list-alt me-2"></i> Điểm số theo môn học
        </h5>
    </div>
    <div class="card-body">
        @if (Model.Grades.Any())
        {
            <div class="row g-4">
                @foreach (var grade in Model.Grades)
                {
                    <div class="col-md-6 col-lg-4">
                        <div class="subject-grade-card animate__animated animate__fadeIn" style="animation-delay: @(Model.Grades.ToList().IndexOf(grade) * 0.1)s;">
                            @{
                                var score = grade.TotalScore;
                                var isPassed = score >= 5.0m;
                                var gradeClass = score >= 8.0m ? "excellent" : score >= 6.5m ? "good" : score >= 5.0m ? "average" : "poor";
                                var gradeText = grade.LetterGrade;
                                var gradientClass = isPassed ? "linear-gradient(135deg, #28a745, #20c997)" : "linear-gradient(135deg, #dc3545, #fd7e14)";
                            }

                            <div class="subject-card-header" style="background: @gradientClass;">
                                <div class="subject-icon">
                                    <i class="fas fa-book-open"></i>
                                </div>
                                <div class="subject-info">
                                    <h6 class="subject-name">@grade.SubjectName</h6>
                                    <div class="subject-status">
                                        <i class="fas @(isPassed ? "fa-check-circle" : "fa-times-circle") me-1"></i>
                                        @(isPassed ? "Đã qua môn" : "Chưa qua môn")
                                    </div>
                                </div>
                            </div>

                            <div class="subject-card-body">
                                <div class="grade-display-section">
                                    <div class="score-circle @gradeClass">
                                        <div class="score-value">@score.ToString("F1")</div>
                                        <div class="score-scale">/10</div>
                                    </div>
                                    <div class="grade-info">
                                        <div class="grade-letter">@gradeText</div>
                                        <div class="grade-gpa">GPA: @grade.GradePoint.ToString("F2")</div>
                                    </div>
                                </div>

                                <div class="exam-count">
                                    <i class="fas fa-credit-card me-1"></i>
                                    @grade.Credits tín chỉ
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <div class="empty-state">
                    <div class="empty-state-icon mb-4">
                        <i class="fas fa-star fa-4x" style="color: #a8e6cf;"></i>
                    </div>
                    <h4 class="text-muted mb-3">Chưa có điểm số nào</h4>
                    <p class="text-muted mb-4">Bạn chưa có điểm số cho môn học nào. Hãy tham gia các bài kiểm tra để có điểm số.</p>
                    <a href="@Url.Action("AvailableExams")" class="btn btn-primary-gradient btn-lg rounded-pill px-4">
                        <i class="fas fa-file-alt me-2"></i>Xem bài thi có sẵn
                    </a>
                </div>
            </div>
        }
    </div>
</div>

<!-- Grade System Modal -->
<div class="modal fade" id="gradeSystemModal" tabindex="-1" aria-labelledby="gradeSystemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="gradeSystemModalLabel">
                    <i class="fas fa-info-circle me-2"></i>Hệ thống chấm điểm
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Thang điểm 10:</h6>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Điểm số</th>
                                    <th>Xếp loại</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr><td>8.5 - 10</td><td class="text-success">Giỏi (A)</td></tr>
                                <tr><td>8.0 - 8.4</td><td class="text-primary">Khá giỏi (B+)</td></tr>
                                <tr><td>7.0 - 7.9</td><td class="text-info">Khá (B)</td></tr>
                                <tr><td>6.5 - 6.9</td><td class="text-warning">Trung bình khá (C+)</td></tr>
                                <tr><td>5.5 - 6.4</td><td class="text-warning">Trung bình (C)</td></tr>
                                <tr><td>5.0 - 5.4</td><td class="text-danger">Trung bình yếu (D+)</td></tr>
                                <tr><td>4.0 - 4.9</td><td class="text-danger">Yếu (D)</td></tr>
                                <tr><td>< 4.0</td><td class="text-danger">Kém (F)</td></tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Thang điểm 4.0 (GPA):</h6>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Điểm 10</th>
                                    <th>GPA</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr><td>8.5 - 10</td><td>3.7 - 4.0</td></tr>
                                <tr><td>8.0 - 8.4</td><td>3.5 - 3.6</td></tr>
                                <tr><td>7.0 - 7.9</td><td>3.0 - 3.4</td></tr>
                                <tr><td>6.5 - 6.9</td><td>2.5 - 2.9</td></tr>
                                <tr><td>5.5 - 6.4</td><td>2.0 - 2.4</td></tr>
                                <tr><td>5.0 - 5.4</td><td>1.5 - 1.9</td></tr>
                                <tr><td>4.0 - 4.9</td><td>1.0 - 1.4</td></tr>
                                <tr><td>< 4.0</td><td>0.0 - 0.9</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

<style>
    .student-info .info-item {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 10px;
        border-left: 4px solid #a8e6cf;
    }

    .info-label {
        font-size: 12px;
        font-weight: bold;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 5px;
    }

    .info-value {
        font-size: 16px;
        font-weight: 500;
        color: #333;
    }

    .gpa-display {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
    }

    .gpa-card {
        background: linear-gradient(135deg, #a8e6cf, #7fcdcd);
        color: white;
        padding: 30px;
        border-radius: 20px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(168, 230, 207, 0.3);
        transition: all 0.3s ease;
    }

    .gpa-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 35px rgba(168, 230, 207, 0.4);
    }

    .gpa-icon {
        font-size: 30px;
        margin-bottom: 15px;
        opacity: 0.9;
    }

    .gpa-value {
        font-size: 36px;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .gpa-label {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 5px;
    }

    .gpa-scale {
        font-size: 12px;
        opacity: 0.8;
    }

    .subject-grade-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .subject-grade-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    .subject-card-header {
        padding: 20px;
        color: white;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .subject-icon {
        width: 50px;
        height: 50px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
    }

    .subject-name {
        margin: 0;
        font-size: 18px;
        font-weight: bold;
    }

    .subject-status {
        margin: 0;
        opacity: 0.9;
        font-size: 14px;
    }

    .subject-card-body {
        padding: 20px;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .grade-display-section {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 20px;
        margin-bottom: 20px;
    }

    .score-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: white;
        position: relative;
    }

    .score-circle.excellent {
        background: linear-gradient(135deg, #28a745, #20c997);
    }

    .score-circle.good {
        background: linear-gradient(135deg, #007bff, #6610f2);
    }

    .score-circle.average {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
    }

    .score-circle.poor {
        background: linear-gradient(135deg, #dc3545, #fd7e14);
    }

    .score-value {
        font-size: 20px;
        line-height: 1;
    }

    .score-scale {
        font-size: 12px;
        opacity: 0.8;
    }

    .grade-info {
        text-align: center;
    }

    .grade-letter {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
    }

    .grade-gpa {
        font-size: 14px;
        color: #666;
    }

    .exam-count {
        text-align: center;
        color: #666;
        font-size: 14px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .empty-state {
        animation: fadeInUp 0.6s ease-out;
    }

    .empty-state-icon {
        animation: float 3s ease-in-out infinite;
    }

    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @@keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-10px);
        }
    }

    @@media (max-width: 768px) {
        .gpa-card {
            padding: 20px;
        }

        .gpa-value {
            font-size: 28px;
        }

        .subject-card-header {
            padding: 15px;
        }

        .subject-card-body {
            padding: 15px;
        }

        .subject-icon {
            width: 40px;
            height: 40px;
            font-size: 16px;
        }

        .subject-name {
            font-size: 16px;
        }

        .grade-display-section {
            flex-direction: column;
            gap: 15px;
        }

        .score-circle {
            width: 70px;
            height: 70px;
        }

        .score-value {
            font-size: 18px;
        }
    }
</style>

@section Scripts {
<script>
    // Enhanced Student Grades Page
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize animations
        initializeAnimations();

        // Initialize grade visualization
        initializeGradeVisualization();
    });

    function initializeAnimations() {
        // Animate subject grade cards
        const gradeCards = document.querySelectorAll('.subject-grade-card');
        gradeCards.forEach((card, index) => {
            card.style.animationDelay = (index * 0.1) + 's';
        });

        // Animate stat cards
        const statCards = document.querySelectorAll('.stat-card-modern');
        statCards.forEach((card, index) => {
            card.style.animationDelay = (index * 0.1 + 0.5) + 's';
            card.classList.add('animate__animated', 'animate__fadeInUp');
        });

        // Animate GPA card
        const gpaCard = document.querySelector('.gpa-card');
        if (gpaCard) {
            gpaCard.classList.add('animate__animated', 'animate__pulse');
        }
    }

    function initializeGradeVisualization() {
        // Add hover effects to score circles
        const scoreCircles = document.querySelectorAll('.score-circle');
        scoreCircles.forEach(circle => {
            circle.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.1)';
            });

            circle.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });
    }
</script>
}
</div> <!-- End student-page-container -->
