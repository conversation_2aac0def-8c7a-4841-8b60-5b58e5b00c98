@model IEnumerable<StudentManagementSystem.Models.StudentGrade>
@{
    ViewData["Title"] = "Quản lý điểm số";
}

@section Styles {
    <link href="~/css/admin-ui.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet" />
}

<!-- Welcome Header with Animation -->
<div class="welcome-header mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <div class="position-relative z-index-2">
            <h2 class="mb-2 animate__animated animate__fadeInLeft fw-bold">
                <i class="fas fa-star me-2"></i>
                Quản lý điểm số ⭐
            </h2>
            <p class="mb-0 opacity-75">Quản lý và theo dõi điểm số của học sinh</p>
        </div>
        <div class="text-end position-relative z-index-2">
            <a href="@Url.Action("CreateStudentGrade")" class="btn btn-light btn-lg px-4 py-2 rounded-pill shadow-sm">
                <i class="fas fa-plus me-2"></i> Thêm điểm số mới
            </a>
        </div>
    </div>
</div>

<div class="admin-action-card animate__animated animate__fadeInUp">
    <div class="card-header">
        <h5 class="mb-0 fw-bold">
            <i class="fas fa-list me-2"></i> Danh sách điểm số
        </h5>
    </div>
    <div class="card-body">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr style="background: linear-gradient(135deg, #a8e6cf, #7fcdcd); color: #2e7d32;">
                            <th class="border-0 fw-bold">Học sinh</th>
                            <th class="border-0 fw-bold">Môn học</th>
                            <th class="border-0 fw-bold">Điểm giữa kỳ</th>
                            <th class="border-0 fw-bold">Điểm cuối kỳ</th>
                            <th class="border-0 fw-bold">Điểm tổng kết</th>
                            <th class="border-0 fw-bold">Học kỳ</th>
                            <th class="border-0 fw-bold">Năm học</th>
                            <th class="border-0 fw-bold">Người tạo</th>
                            <th class="border-0 fw-bold text-center">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var grade in Model)
                        {
                            <tr class="animate__animated animate__fadeIn" style="animation-delay: @(Model.ToList().IndexOf(grade) * 0.1)s;">
                                <td>
                                    @if (grade.Student != null)
                                    {
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-2">
                                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                                    <i class="fas fa-user text-muted"></i>
                                                </div>
                                            </div>
                                            <div>
                                                <div class="fw-bold">@grade.Student.FullName</div>
                                                <small class="text-muted">@grade.Student.StudentId</small>
                                            </div>
                                        </div>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Không xác định</span>
                                    }
                                </td>
                                <td>
                                    @if (grade.Subject != null)
                                    {
                                        <div>
                                            <div class="fw-bold text-primary">@grade.Subject.SubjectName</div>
                                            <small class="text-muted">@grade.Subject.SubjectCode</small>
                                        </div>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Không xác định</span>
                                    }
                                </td>
                                <td class="text-center">
                                    @if (grade.MidtermScore > 0)
                                    {
                                        <span class="badge bg-info rounded-pill px-3">@grade.MidtermScore.ToString("F1")</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Chưa có</span>
                                    }
                                </td>
                                <td class="text-center">
                                    @if (grade.FinalScore > 0)
                                    {
                                        <span class="badge bg-warning rounded-pill px-3">@grade.FinalScore.ToString("F1")</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Chưa có</span>
                                    }
                                </td>
                                <td class="text-center">
                                    @{
                                        var totalScore = grade.TotalScore;
                                        var badgeClass = totalScore >= 8.0m ? "bg-success" :
                                                        totalScore >= 6.5m ? "bg-primary" :
                                                        totalScore >= 5.0m ? "bg-warning" : "bg-danger";
                                        var gradeText = totalScore >= 8.0m ? "Giỏi" :
                                                       totalScore >= 6.5m ? "Khá" :
                                                       totalScore >= 5.0m ? "TB" : "Yếu";
                                    }
                                    <div>
                                        <span class="badge @badgeClass rounded-pill px-3 mb-1">@totalScore.ToString("F1")</span>
                                        <br><small class="text-muted">@gradeText</small>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-light text-dark rounded-pill">@grade.Semester</span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-secondary rounded-pill">@grade.Year</span>
                                </td>
                                <td>
                                    @if (grade.CreatedBy != null)
                                    {
                                        <div class="text-center">
                                            <div class="fw-bold">@grade.CreatedBy.FullName</div>
                                            <small class="text-muted">@grade.CreatedAt.ToString("dd/MM/yyyy")</small>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="text-center">
                                            <div class="text-muted">
                                                <i class="fas fa-robot me-1"></i>Hệ thống
                                            </div>
                                            <small class="text-muted">@grade.CreatedAt.ToString("dd/MM/yyyy")</small>
                                        </div>
                                    }
                                </td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <a href="@Url.Action("EditStudentGrade", new { id = grade.Id })"
                                           class="btn btn-sm btn-outline-primary rounded-pill me-1"
                                           data-bs-toggle="tooltip"
                                           title="Chỉnh sửa điểm số">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-sm btn-outline-danger rounded-pill"
                                                data-bs-toggle="modal"
                                                data-bs-target="#<EMAIL>"
                                                title="Xóa điểm số">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <div class="empty-state">
                    <div class="empty-state-icon mb-4">
                        <i class="fas fa-star fa-4x" style="color: #a8e6cf;"></i>
                    </div>
                    <h4 class="text-muted mb-3">Chưa có điểm số nào</h4>
                    <p class="text-muted mb-4">Hãy thêm điểm số đầu tiên cho hệ thống</p>
                    <a href="@Url.Action("CreateStudentGrade")" class="btn btn-success-gradient btn-lg rounded-pill px-4">
                        <i class="fas fa-plus me-2"></i> Thêm điểm số mới
                    </a>
                </div>
            </div>
        }
    </div>
</div>

<!-- Delete Modals -->
@foreach (var grade in Model)
{
    <div class="modal fade" id="<EMAIL>" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Xác nhận xóa</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Bạn có chắc chắn muốn xóa điểm số của <strong>@grade.Student.FullName</strong> môn <strong>@grade.Subject.SubjectName</strong>?</p>
                    <p class="text-danger"><small>Hành động này không thể hoàn tác!</small></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <form asp-action="DeleteStudentGrade" method="post" class="d-inline">
                        <input type="hidden" name="id" value="@grade.Id" />
                        <button type="submit" class="btn btn-danger">Xóa</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
}
