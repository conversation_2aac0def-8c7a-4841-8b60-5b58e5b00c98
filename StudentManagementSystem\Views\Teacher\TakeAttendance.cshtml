@model StudentManagementSystem.ViewModels.TakeAttendanceViewModel
@{
    ViewData["Title"] = "Điểm danh";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-check-circle"></i> <PERSON><PERSON><PERSON><PERSON> danh</h2>
        <p class="text-muted mb-0">
            @Model.Session.SessionTitle - @Model.Session.SessionDate.ToString("dd/MM/yyyy") 
            (@Model.Session.StartTime.ToString(@"hh\:mm") - @Model.Session.EndTime.ToString(@"hh\:mm"))
        </p>
    </div>
    <div>
        <a href="@Url.Action("ClassDetails", new { id = Model.Class.Id })" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>
</div>

@if (TempData["Success"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        @TempData["Success"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-users"></i> Danh sách sinh viên (@Model.StudentAttendances.Count)</h5>
                    <div>
                        <button type="button" class="btn btn-sm btn-success" onclick="markAllPresent()">
                            <i class="fas fa-check-double"></i> Có mặt tất cả
                        </button>
                        <button type="button" class="btn btn-sm btn-warning" onclick="markAllAbsent()">
                            <i class="fas fa-times"></i> Vắng tất cả
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <form asp-action="TakeAttendance" method="post" id="attendanceForm">
                    <input type="hidden" name="sessionId" value="@Model.Session.Id" />
                    
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>STT</th>
                                    <th>Mã SV</th>
                                    <th>Họ tên</th>
                                    <th>Có mặt</th>
                                    <th>Đi muộn</th>
                                    <th>Vắng có phép</th>
                                    <th>Ghi chú</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (int i = 0; i < Model.StudentAttendances.Count; i++)
                                {
                                    var student = Model.StudentAttendances[i];
                                    <tr>
                                        <td>@(i + 1)</td>
                                        <td>
                                            <strong>@student.StudentId</strong>
                                            <input type="hidden" name="studentAttendances[@i].StudentUserId" value="@student.StudentUserId" />
                                            <input type="hidden" name="studentAttendances[@i].StudentName" value="@student.StudentName" />
                                            <input type="hidden" name="studentAttendances[@i].StudentId" value="@student.StudentId" />
                                        </td>
                                        <td>@student.StudentName</td>
                                        <td>
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input present-checkbox" 
                                                       name="studentAttendances[@i].IsPresent" value="true" 
                                                       @(student.IsPresent ? "checked" : "") 
                                                       onchange="updateAttendanceStatus(@i)" />
                                                <input type="hidden" name="studentAttendances[@i].IsPresent" value="false" />
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input late-checkbox" 
                                                       name="studentAttendances[@i].IsLate" value="true" 
                                                       @(student.IsLate ? "checked" : "") 
                                                       @(!student.IsPresent ? "disabled" : "") />
                                                <input type="hidden" name="studentAttendances[@i].IsLate" value="false" />
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input excused-checkbox" 
                                                       name="studentAttendances[@i].IsExcused" value="true" 
                                                       @(student.IsExcused ? "checked" : "") 
                                                       @(student.IsPresent ? "disabled" : "") />
                                                <input type="hidden" name="studentAttendances[@i].IsExcused" value="false" />
                                            </div>
                                        </td>
                                        <td>
                                            <input type="text" class="form-control form-control-sm" 
                                                   name="studentAttendances[@i].Note" value="@student.Note" 
                                                   placeholder="Ghi chú..." />
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-3">
                        <div>
                            <span class="badge bg-success me-2">Có mặt: <span id="presentCount">0</span></span>
                            <span class="badge bg-warning me-2">Đi muộn: <span id="lateCount">0</span></span>
                            <span class="badge bg-info me-2">Vắng có phép: <span id="excusedCount">0</span></span>
                            <span class="badge bg-danger">Vắng không phép: <span id="absentCount">0</span></span>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Lưu điểm danh
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> Thông tin buổi học</h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-4">Lớp:</dt>
                    <dd class="col-sm-8">@Model.Class.ClassName</dd>
                    
                    <dt class="col-sm-4">Môn học:</dt>
                    <dd class="col-sm-8">
                        @foreach (var classSubject in Model.Class.ClassSubjects)
                        {
                            <span class="badge bg-primary me-1">@classSubject.Subject.SubjectName</span>
                        }
                    </dd>
                    
                    <dt class="col-sm-4">Ngày:</dt>
                    <dd class="col-sm-8">@Model.Session.SessionDate.ToString("dd/MM/yyyy")</dd>
                    
                    <dt class="col-sm-4">Thời gian:</dt>
                    <dd class="col-sm-8">
                        @Model.Session.StartTime.ToString(@"hh\:mm") - @Model.Session.EndTime.ToString(@"hh\:mm")
                    </dd>
                    
                    <dt class="col-sm-4">Phòng:</dt>
                    <dd class="col-sm-8">@Model.Session.Location</dd>
                    
                    <dt class="col-sm-4">Mô tả:</dt>
                    <dd class="col-sm-8">@(string.IsNullOrEmpty(Model.Session.Description) ? "Không có" : Model.Session.Description)</dd>
                </dl>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-clock"></i> Thời gian hiện tại</h5>
            </div>
            <div class="card-body text-center">
                <h4 id="currentTime" class="text-primary"></h4>
                <p class="text-muted mb-0">Cập nhật mỗi giây</p>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Update current time
        function updateCurrentTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = now.toLocaleTimeString('vi-VN');
        }
        
        setInterval(updateCurrentTime, 1000);
        updateCurrentTime();
        
        // Update attendance counts
        function updateCounts() {
            const presentCount = document.querySelectorAll('.present-checkbox:checked').length;
            const lateCount = document.querySelectorAll('.late-checkbox:checked').length;
            const excusedCount = document.querySelectorAll('.excused-checkbox:checked').length;
            const totalCount = document.querySelectorAll('.present-checkbox').length;
            const absentCount = totalCount - presentCount;
            
            document.getElementById('presentCount').textContent = presentCount;
            document.getElementById('lateCount').textContent = lateCount;
            document.getElementById('excusedCount').textContent = excusedCount;
            document.getElementById('absentCount').textContent = absentCount;
        }
        
        // Update attendance status when present checkbox changes
        function updateAttendanceStatus(index) {
            const presentCheckbox = document.querySelector(`input[name="studentAttendances[${index}].IsPresent"]`);
            const lateCheckbox = document.querySelector(`input[name="studentAttendances[${index}].IsLate"]`);
            const excusedCheckbox = document.querySelector(`input[name="studentAttendances[${index}].IsExcused"]`);
            
            if (presentCheckbox.checked) {
                lateCheckbox.disabled = false;
                excusedCheckbox.disabled = true;
                excusedCheckbox.checked = false;
            } else {
                lateCheckbox.disabled = true;
                lateCheckbox.checked = false;
                excusedCheckbox.disabled = false;
            }
            
            updateCounts();
        }
        
        // Mark all present
        function markAllPresent() {
            document.querySelectorAll('.present-checkbox').forEach((checkbox, index) => {
                checkbox.checked = true;
                updateAttendanceStatus(index);
            });
        }
        
        // Mark all absent
        function markAllAbsent() {
            document.querySelectorAll('.present-checkbox').forEach((checkbox, index) => {
                checkbox.checked = false;
                updateAttendanceStatus(index);
            });
        }
        
        // Initialize counts and status
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.present-checkbox').forEach((checkbox, index) => {
                updateAttendanceStatus(index);
            });
            
            // Add event listeners for count updates
            document.querySelectorAll('.present-checkbox, .late-checkbox, .excused-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', updateCounts);
            });
        });
    </script>
}
