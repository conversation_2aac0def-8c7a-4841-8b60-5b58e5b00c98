@model StudentManagementSystem.ViewModels.CreateStudentViewModel
@{
    ViewData["Title"] = "Thêm học sinh mới";
}

<div class="admin-page-container">
    <!-- Welcome Header -->
    <div class="welcome-header mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div class="position-relative z-index-2">
                <h2 class="mb-2 fw-bold">
                    <i class="fas fa-user-plus me-2"></i>
                    Thêm học sinh mới 🎓
                </h2>
                <p class="mb-0 opacity-75">Tạo tài khoản mới cho học sinh trong hệ thống</p>
            </div>
            <div class="text-end position-relative z-index-2">
                <a href="@Url.Action("Students")" class="btn btn-light btn-lg px-4 py-2 rounded-pill shadow-sm">
                    <i class="fas fa-arrow-left me-2"></i> <PERSON>uay lại danh sách
                </a>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="mb-0 text-primary fw-bold">
                        <i class="fas fa-user-plus me-2"></i> Thông tin học sinh
                    </h5>
                </div>
            <div class="card-body">
                <form asp-action="CreateStudent" method="post" class="form-modern">
                    <div asp-validation-summary="All" class="alert alert-danger" role="alert"></div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group-modern">
                                <label asp-for="FirstName" class="form-label-modern"></label>
                                <input asp-for="FirstName" class="form-control-modern" placeholder="Nhập tên" />
                                <span asp-validation-for="FirstName" class="form-error"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group-modern">
                                <label asp-for="LastName" class="form-label-modern"></label>
                                <input asp-for="LastName" class="form-control-modern" placeholder="Nhập họ" />
                                <span asp-validation-for="LastName" class="form-error"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group-modern">
                                <label asp-for="Email" class="form-label-modern"></label>
                                <input asp-for="Email" class="form-control-modern" type="email" placeholder="<EMAIL>" />
                                <span asp-validation-for="Email" class="form-error"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group-modern">
                                <label asp-for="StudentId" class="form-label-modern"></label>
                                <input asp-for="StudentId" class="form-control-modern" placeholder="VD: SV001" />
                                <span asp-validation-for="StudentId" class="form-error"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Password" class="form-label"></label>
                                <input asp-for="Password" class="form-control" type="password" placeholder="Nhập mật khẩu" />
                                <span asp-validation-for="Password" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="ConfirmPassword" class="form-label"></label>
                                <input asp-for="ConfirmPassword" class="form-control" type="password" placeholder="Xác nhận mật khẩu" />
                                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="DateOfBirth" class="form-label"></label>
                                <input asp-for="DateOfBirth" class="form-control" type="date" />
                                <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="ClassId" class="form-label"></label>
                                <select asp-for="ClassId" class="form-select">
                                    <option value="">-- Chọn lớp học (tùy chọn) --</option>
                                    @foreach (var classItem in ViewBag.Classes as IEnumerable<StudentManagementSystem.Models.Class>)
                                    {
                                        <option value="@classItem.Id">@classItem.ClassName - @classItem.Semester/@classItem.Year</option>
                                    }
                                </select>
                                <span asp-validation-for="ClassId" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="Address" class="form-label"></label>
                        <textarea asp-for="Address" class="form-control" rows="3" placeholder="Nhập địa chỉ (tùy chọn)"></textarea>
                        <span asp-validation-for="Address" class="text-danger"></span>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="@Url.Action("Students")" class="btn btn-outline-secondary btn-lg px-4 rounded-pill">
                            <i class="fas fa-arrow-left me-2"></i> Quay lại
                        </a>
                        <button type="submit" class="btn btn-primary btn-lg px-4 rounded-pill">
                            <i class="fas fa-save me-2"></i> Tạo học sinh
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div> <!-- End admin-page-container -->

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
